### Test file for new features added to CMS Backend
### Make sure to replace {{baseUrl}} with your actual server URL (e.g., http://localhost:3000)
### Replace {{authToken}} with a valid JWT token

@baseUrl = http://localhost:3000
@authToken = your-jwt-token-here

### ============================================
### AFFILIATE SYSTEM TESTS
### ============================================

### 1. Generate affiliate code for a user
POST {{baseUrl}}/affiliate/generate/{{userId}}
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
  "customCode": "AFF-JOHN-2024"
}

### 2. Get affiliate statistics for a user
GET {{baseUrl}}/affiliate/stats/{{userId}}
Authorization: Bearer {{authToken}}

### 3. Get current user's affiliate statistics
GET {{baseUrl}}/affiliate/my-stats
Authorization: Bearer {{authToken}}

### 4. Get users referred by an affiliate
GET {{baseUrl}}/affiliate/referrals/{{userId}}?page=1&limit=10
Authorization: Bearer {{authToken}}

### 5. Get current user's referrals
GET {{baseUrl}}/affiliate/my-referrals?page=1&limit=10
Authorization: Bearer {{authToken}}

### 6. Toggle affiliate code status
PUT {{baseUrl}}/affiliate/toggle/{{userId}}
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
  "active": true
}

### 7. Update affiliate code
PUT {{baseUrl}}/affiliate/update/{{userId}}
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
  "newCode": "AFF-JOHN-2024-UPDATED"
}

### 8. Register with affiliate code
POST {{baseUrl}}/auth/register
Content-Type: application/json

{
  "username": "newuser123",
  "fullName": "New User",
  "email": "<EMAIL>",
  "password": "password123",
  "referrerAffiliateCode": "AFF-JOHN-2024"
}

### ============================================
### UPLOAD SYSTEM TESTS
### ============================================

### 9. Upload single image
POST {{baseUrl}}/upload/image
Authorization: Bearer {{authToken}}
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="file"; filename="test-image.jpg"
Content-Type: image/jpeg

< ./path/to/your/test-image.jpg
------WebKitFormBoundary7MA4YWxkTrZu0gW--

### 10. Upload multiple images
POST {{baseUrl}}/upload/images
Authorization: Bearer {{authToken}}
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="files"; filename="test-image1.jpg"
Content-Type: image/jpeg

< ./path/to/your/test-image1.jpg
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="files"; filename="test-image2.jpg"
Content-Type: image/jpeg

< ./path/to/your/test-image2.jpg
------WebKitFormBoundary7MA4YWxkTrZu0gW--

### 11. Get uploaded image
GET {{baseUrl}}/upload/image/{{filename}}
Authorization: Bearer {{authToken}}

### 12. Get file information
GET {{baseUrl}}/upload/info/{{filename}}?category=images
Authorization: Bearer {{authToken}}

### 13. Delete uploaded image
DELETE {{baseUrl}}/upload/image/{{filename}}
Authorization: Bearer {{authToken}}

### 14. Cleanup temporary files (Admin only)
POST {{baseUrl}}/upload/cleanup?hours=24
Authorization: Bearer {{authToken}}

### ============================================
### STATIC FILE ACCESS TESTS
### ============================================

### 15. Access image directly via static URL
GET {{baseUrl}}/uploads/images/{{filename}}

### 16. Access document directly via static URL
GET {{baseUrl}}/uploads/documents/{{filename}}

### ============================================
### EXISTING ENDPOINTS WITH AFFILIATE SUPPORT
### ============================================

### 17. Create user with affiliate information
POST {{baseUrl}}/users
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
  "username": "testuser456",
  "fullName": "Test User",
  "email": "<EMAIL>",
  "password": "password123",
  "affiliateCode": "AFF-TEST-2024",
  "referredBy": "{{referrerUserId}}"
}

### 18. Get user with affiliate information
GET {{baseUrl}}/users/{{userId}}
Authorization: Bearer {{authToken}}

### 19. Update user with affiliate information
PATCH {{baseUrl}}/users/{{userId}}
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
  "affiliateCode": "AFF-UPDATED-2024"
}

### ============================================
### LOGGING VERIFICATION
### ============================================
### Check the log files in the /logs directory to verify logging is working:
### - logs/combined-YYYY-MM-DD.log - All logs
### - logs/error-YYYY-MM-DD.log - Error logs only
### - logs/access-YYYY-MM-DD.log - HTTP access logs

### ============================================
### ERROR TESTING
### ============================================

### 20. Test duplicate affiliate code error
POST {{baseUrl}}/affiliate/generate/{{userId}}
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
  "customCode": "AFF-EXISTING-CODE"
}

### 21. Test invalid file upload
POST {{baseUrl}}/upload/image
Authorization: Bearer {{authToken}}
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="file"; filename="test.txt"
Content-Type: text/plain

This is a text file, not an image
------WebKitFormBoundary7MA4YWxkTrZu0gW--

### 22. Test file too large (if you have a file > 10MB)
POST {{baseUrl}}/upload/image
Authorization: Bearer {{authToken}}
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="file"; filename="large-file.jpg"
Content-Type: image/jpeg

< ./path/to/large-file.jpg
------WebKitFormBoundary7MA4YWxkTrZu0gW--

### 23. Test invalid affiliate code during registration
POST {{baseUrl}}/auth/register
Content-Type: application/json

{
  "username": "testuser789",
  "fullName": "Test User",
  "email": "<EMAIL>",
  "password": "password123",
  "referrerAffiliateCode": "INVALID-CODE"
}

### ============================================
### NOTES
### ============================================
### 1. Replace {{userId}} with actual user IDs from your database
### 2. Replace {{filename}} with actual filenames returned from upload operations
### 3. Replace {{authToken}} with a valid JWT token from login
### 4. Make sure you have appropriate permissions for each operation
### 5. Check the logs directory for detailed logging information
### 6. For file uploads, make sure the file paths exist and are accessible
