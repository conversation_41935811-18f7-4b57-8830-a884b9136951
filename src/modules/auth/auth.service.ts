import { Injectable, UnauthorizedException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import * as bcrypt from 'bcryptjs';

import { User } from '@/entities/user.entity';
import { LoginDto } from './dto/login.dto';
import { RegisterDto } from './dto/register.dto';
import { AffiliateService } from '../affiliate/services/affiliate.service';
import { AffiliateUsageService, TrackUsageDto } from '../affiliate/services/affiliate-usage.service';
import { AffiliateUsageType, AffiliateUsageStatus } from '@/entities/affiliate-usage.entity';
import { CustomLoggerService } from '@/common/logger/logger.service';
import { AuditLogService } from '@/common/services/audit-log.service';
import { AuditAction } from '@/entities/audit-log.entity';

@Injectable()
export class AuthService {
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly jwtService: JwtService,
    private readonly affiliateService: AffiliateService,
    private readonly affiliateUsageService: AffiliateUsageService,
    private readonly logger: CustomLoggerService,
    private readonly auditLogService: AuditLogService,
  ) {}

  async validateUser(username: string, password: string): Promise<any> {
    this.logger.info(`Login attempt for user: ${username}`, 'AuthService');

    const user = await this.userRepository.findOne({
      where: [
        { username: username },
        { email: username }
      ],
      relations: ['userRoles', 'userRoles.role', 'userRoles.role.rolePermissions', 'userRoles.role.rolePermissions.permission'],
    });

    if (user && (await bcrypt.compare(password, user.password))) {
      this.logger.info(`Login successful for user: ${username}`, 'AuthService');
      const { password, ...result } = user;
      return result;
    }

    this.logger.warn(`Login failed for user: ${username}`, 'AuthService');
    return null;
  }

  async login(loginDto: LoginDto) {
    const { username, password } = loginDto;
    const user = await this.validateUser(username, password);

    if (!user) {
      throw new UnauthorizedException('Invalid credentials');
    }

    // Get unique permissions from all user roles
    const permissions = new Set<string>();
    user.userRoles?.forEach((userRole: any) => {
      userRole.role.rolePermissions?.forEach((rolePermission: any) => {
        permissions.add(rolePermission.permission.action);
      });
    });

    const payload = {
      sub: user.id,
      email: user.email,
      roles: user.userRoles?.map((ura: any) => ura.role.name) || [],
      permissions: Array.from(permissions),
    };

    return {
      access_token: this.jwtService.sign(payload),
      user: {
        id: user.id,
        email: user.email,
        username: user.username,
        fullName: user.fullName,
        roles: user.userRoles?.map((ura: any) => ura.role.name) || [],
        permissions: Array.from(permissions),
      },
    };
  }

  async register(registerDto: RegisterDto) {
    const { email, password, username, fullName, phone, address, city, state, zip, country, referrerAffiliateCode } = registerDto;

    this.logger.info(`Registration attempt for user: ${username}`, 'AuthService', { email, username });

    // Check if user already exists
    const existingUser = await this.userRepository.findOne({
      where: [{ email }, { username }],
    });

    if (existingUser) {
      this.logger.warn(`Registration failed - user already exists: ${username}`, 'AuthService');
      throw new UnauthorizedException('User already exists');
    }

    try {
      // Hash password
      const hashedPassword = await bcrypt.hash(password, 12);

      // Create new user
      const user = this.userRepository.create({
        email,
        username,
        fullName,
        password: hashedPassword,
        phone,
        address,
        city,
        state,
        zip,
        country,
      });

      const savedUser = await this.userRepository.save(user);

      // Process referral if affiliate code is provided
      if (referrerAffiliateCode) {
        try {
          // Track affiliate usage
          const trackUsageDto: TrackUsageDto = {
            affiliateCode: referrerAffiliateCode,
            usageType: AffiliateUsageType.REGISTRATION,
            metadata: { registeredUserId: savedUser.id },
          };

          const usage = await this.affiliateUsageService.trackUsage(trackUsageDto);

          // Process referral
          await this.affiliateService.processReferral(savedUser.id, referrerAffiliateCode);

          // Update usage status to success
          await this.affiliateUsageService.updateUsageStatus(usage.id, {
            status: AffiliateUsageStatus.SUCCESS,
            userId: savedUser.id,
          });

          this.logger.info(`Referral processed during registration: ${username}`, 'AuthService', { referrerAffiliateCode });
        } catch (error) {
          this.logger.error(`Failed to process referral during registration: ${username}`, error.stack, 'AuthService');
        }
      }

      // Log registration audit
      await this.auditLogService.logAuthEvent(
        AuditAction.CREATE,
        savedUser.id,
        savedUser.username,
        true
      );

      this.logger.info(`User registered successfully: ${username}`, 'AuthService', { userId: savedUser.id });

      const { password: _, ...result } = savedUser;
      return result;
    } catch (error) {
      this.logger.error(`Registration failed for user: ${username}`, error.stack, 'AuthService');
      throw error;
    }
  }

  async refreshToken(userId: string) {
    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: ['userRoles', 'userRoles.role', 'userRoles.role.rolePermissions', 'userRoles.role.rolePermissions.permission'],
    });

    if (!user) {
      throw new UnauthorizedException('User not found');
    }

    // Get unique permissions from all user roles
    const permissions = new Set<string>();
    user.userRoles?.forEach((userRole: any) => {
      userRole.role.rolePermissions?.forEach((rolePermission: any) => {
        permissions.add(rolePermission.permission.action);
      });
    });

    const payload = {
      sub: user.id,
      email: user.email,
      roles: user.userRoles?.map((ura: any) => ura.role.name) || [],
      permissions: Array.from(permissions),
    };

    return {
      access_token: this.jwtService.sign(payload),
    };
  }
} 