import { Injectable, UnauthorizedException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import * as bcrypt from 'bcryptjs';

import { User } from '@/entities/user.entity';
import { LoginDto } from './dto/login.dto';
import { RegisterDto } from './dto/register.dto';

@Injectable()
export class AuthService {
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly jwtService: JwtService,
  ) {}

  async validateUser(username: string, password: string): Promise<any> {
    const user = await this.userRepository.findOne({
      where: [
        { username: username },
        { email: username }
      ],
      relations: ['userRoles', 'userRoles.role', 'userRoles.role.rolePermissions', 'userRoles.role.rolePermissions.permission'],
    });

    if (user && (await bcrypt.compare(password, user.password))) {
      const { password, ...result } = user;
      return result;
    }
    return null;
  }

  async login(loginDto: LoginDto) {
    const { username, password } = loginDto;
    const user = await this.validateUser(username, password);

    if (!user) {
      throw new UnauthorizedException('Invalid credentials');
    }

    // Get unique permissions from all user roles
    const permissions = new Set<string>();
    user.userRoles?.forEach((userRole: any) => {
      userRole.role.rolePermissions?.forEach((rolePermission: any) => {
        permissions.add(rolePermission.permission.action);
      });
    });

    const payload = {
      sub: user.id,
      email: user.email,
      roles: user.userRoles?.map((ura: any) => ura.role.name) || [],
      permissions: Array.from(permissions),
    };

    return {
      access_token: this.jwtService.sign(payload),
      user: {
        id: user.id,
        email: user.email,
        username: user.username,
        fullName: user.fullName,
        roles: user.userRoles?.map((ura: any) => ura.role.name) || [],
        permissions: Array.from(permissions),
      },
    };
  }

  async register(registerDto: RegisterDto) {
    const { email, password, username, fullName, phone, address, city, state, zip, country } = registerDto;

    // Check if user already exists
    const existingUser = await this.userRepository.findOne({
      where: [{ email }, { username }],
    });

    if (existingUser) {
      throw new UnauthorizedException('User already exists');
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 12);

    // Create new user
    const user = this.userRepository.create({
      email,
      username,
      fullName,
      password: hashedPassword,
      phone,
      address,
      city,
      state,
      zip,
      country,
    });

    const savedUser = await this.userRepository.save(user);

    const { password: _, ...result } = savedUser;
    return result;
  }

  async refreshToken(userId: string) {
    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: ['userRoles', 'userRoles.role', 'userRoles.role.rolePermissions', 'userRoles.role.rolePermissions.permission'],
    });

    if (!user) {
      throw new UnauthorizedException('User not found');
    }

    // Get unique permissions from all user roles
    const permissions = new Set<string>();
    user.userRoles?.forEach((userRole: any) => {
      userRole.role.rolePermissions?.forEach((rolePermission: any) => {
        permissions.add(rolePermission.permission.action);
      });
    });

    const payload = {
      sub: user.id,
      email: user.email,
      roles: user.userRoles?.map((ura: any) => ura.role.name) || [],
      permissions: Array.from(permissions),
    };

    return {
      access_token: this.jwtService.sign(payload),
    };
  }
} 