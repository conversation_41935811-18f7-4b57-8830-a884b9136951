import { Injectable, NotFoundException, ConflictException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { PermissionGroup } from '@/entities/permission-group.entity';
import { Permission } from '@/entities/permission.entity';
import { PermissionGroupDetail } from '@/entities/permission-group-detail.entity';
import { CreatePermissionGroupDto } from './dto/create-permission-group.dto';
import { UpdatePermissionGroupDto } from './dto/update-permission-group.dto';
import { PaginationDto, PaginatedResult, createPaginatedResult } from '@/common/dto/pagination.dto';

@Injectable()
export class PermissionGroupsService {
  constructor(
    @InjectRepository(PermissionGroup)
    private readonly permissionGroupRepository: Repository<PermissionGroup>,
    @InjectRepository(Permission)
    private readonly permissionRepository: Repository<Permission>,
    @InjectRepository(PermissionGroupDetail)
    private readonly permissionGroupDetailRepository: Repository<PermissionGroupDetail>,
  ) {}

  async create(createPermissionGroupDto: CreatePermissionGroupDto, createdBy?: string): Promise<PermissionGroup> {
    // Check if permission group name already exists
    const existingGroup = await this.permissionGroupRepository.findOne({
      where: { name: createPermissionGroupDto.name },
    });
    if (existingGroup) {
      throw new ConflictException('Permission group name already exists');
    }

    // Validate permissions if provided
    if (createPermissionGroupDto.permissionIds && createPermissionGroupDto.permissionIds.length > 0) {
      const permissions = await this.permissionRepository.find({
        where: { id: In(createPermissionGroupDto.permissionIds) },
      });
      if (permissions.length !== createPermissionGroupDto.permissionIds.length) {
        throw new BadRequestException('One or more permissions not found');
      }
    }

    // Create permission group
    const permissionGroup = this.permissionGroupRepository.create({
      name: createPermissionGroupDto.name,
      createdBy,
    });

    const savedGroup = await this.permissionGroupRepository.save(permissionGroup);

    // Create permission group details if permissions are provided
    if (createPermissionGroupDto.permissionIds && createPermissionGroupDto.permissionIds.length > 0) {
      await this.assignPermissions(savedGroup.id, createPermissionGroupDto.permissionIds, createdBy);
    }

    return await this.findOne(savedGroup.id);
  }

  async findAll(paginationDto: PaginationDto): Promise<PaginatedResult<PermissionGroup>> {
    const { page, limit, search, sortBy, sortOrder } = paginationDto;
    
    const queryBuilder = this.permissionGroupRepository.createQueryBuilder('permissionGroup')
      .leftJoinAndSelect('permissionGroup.permissionDetails', 'permissionDetails')
      .leftJoinAndSelect('permissionDetails.permission', 'permission');
    
    // Add search functionality
    if (search) {
      queryBuilder.where('permissionGroup.name LIKE :search', { search: `%${search}%` });
    }

    // Add sorting
    const sortField = sortBy || 'createdAt';
    const sortDirection = sortOrder || 'DESC';
    queryBuilder.orderBy(`permissionGroup.${sortField}`, sortDirection);

    // Add pagination
    const skip = ((page || 1) - 1) * (limit || 10);
    queryBuilder.skip(skip).take(limit || 10);

    // Execute query
    const [permissionGroups, total] = await queryBuilder.getManyAndCount();

    // Transform the data to include permissions directly
    const transformedGroups = permissionGroups.map(group => ({
      ...group,
      permissions: group.permissionDetails?.map(detail => detail.permission) || [],
    }));

    return createPaginatedResult(transformedGroups, total, page || 1, limit || 10);
  }

  async findOne(id: string): Promise<PermissionGroup> {
    const permissionGroup = await this.permissionGroupRepository
      .createQueryBuilder('permissionGroup')
      .leftJoinAndSelect('permissionGroup.permissionDetails', 'permissionDetails')
      .leftJoinAndSelect('permissionDetails.permission', 'permission')
      .where('permissionGroup.id = :id', { id })
      .getOne();

    if (!permissionGroup) {
      throw new NotFoundException(`Permission group with ID ${id} not found`);
    }

    // Transform the data to include permissions directly
    return {
      ...permissionGroup,
      permissions: permissionGroup.permissionDetails?.map(detail => detail.permission) || [],
    } as any;
  }

  async update(id: string, updatePermissionGroupDto: UpdatePermissionGroupDto, updatedBy?: string): Promise<PermissionGroup> {
    const permissionGroup = await this.permissionGroupRepository.findOne({ where: { id } });
    if (!permissionGroup) {
      throw new NotFoundException(`Permission group with ID ${id} not found`);
    }

    // Check if name is being updated and already exists
    if (updatePermissionGroupDto.name && updatePermissionGroupDto.name !== permissionGroup.name) {
      const existingGroup = await this.permissionGroupRepository.findOne({
        where: { name: updatePermissionGroupDto.name },
      });
      if (existingGroup) {
        throw new ConflictException('Permission group name already exists');
      }
    }

    // Update permission group
    if (updatePermissionGroupDto.name) {
      permissionGroup.name = updatePermissionGroupDto.name;
      permissionGroup.updatedBy = updatedBy;
      await this.permissionGroupRepository.save(permissionGroup);
    }

    // Update permissions if provided
    if (updatePermissionGroupDto.permissionIds !== undefined) {
      // Remove existing permissions
      await this.permissionGroupDetailRepository.delete({ permissionGroupId: id });
      
      // Add new permissions
      if (updatePermissionGroupDto.permissionIds.length > 0) {
        await this.assignPermissions(id, updatePermissionGroupDto.permissionIds, updatedBy);
      }
    }

    return await this.findOne(id);
  }

  async remove(id: string, deletedBy?: string): Promise<void> {
    const permissionGroup = await this.permissionGroupRepository.findOne({ where: { id } });
    if (!permissionGroup) {
      throw new NotFoundException(`Permission group with ID ${id} not found`);
    }
    
    // Remove permission group details first
    await this.permissionGroupDetailRepository.delete({ permissionGroupId: id });
    
    // Soft delete permission group
    permissionGroup.deletedBy = deletedBy;
    await this.permissionGroupRepository.softRemove(permissionGroup);
  }

  async assignPermissions(groupId: string, permissionIds: string[], createdBy?: string): Promise<void> {
    // Validate permissions exist
    const permissions = await this.permissionRepository.find({
      where: { id: In(permissionIds) },
    });
    if (permissions.length !== permissionIds.length) {
      throw new BadRequestException('One or more permissions not found');
    }

    // Create permission group details
    const permissionGroupDetails = permissionIds.map(permissionId => 
      this.permissionGroupDetailRepository.create({
        permissionGroupId: groupId,
        permissionId,
        createdBy,
      })
    );

    await this.permissionGroupDetailRepository.save(permissionGroupDetails);
  }

  async removePermissions(groupId: string, permissionIds: string[]): Promise<void> {
    await this.permissionGroupDetailRepository.delete({
      permissionGroupId: groupId,
      permissionId: In(permissionIds),
    });
  }

  async getGroupPermissions(groupId: string): Promise<Permission[]> {
    const permissionGroup = await this.findOne(groupId);
    return (permissionGroup as any).permissions || [];
  }
}
