import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Exclude, Expose, Type } from 'class-transformer';

class PermissionSummaryDto {
  @Expose()
  id: string;

  @Expose()
  action: string;

  @Expose()
  description?: string;
}

export class PermissionGroupResponseDto {
  @ApiProperty({
    description: 'Permission Group ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @Expose()
  id: string;

  @ApiProperty({
    description: 'Permission group name',
    example: 'User Management',
  })
  @Expose()
  name: string;

  @ApiProperty({
    description: 'Creation date',
    example: '2023-01-01T00:00:00.000Z',
  })
  @Expose()
  createdAt: Date;

  @ApiProperty({
    description: 'Last update date',
    example: '2023-01-01T00:00:00.000Z',
  })
  @Expose()
  updatedAt: Date;

  @ApiPropertyOptional({
    description: 'Created by',
    example: 'admin',
  })
  @Expose()
  createdBy?: string;

  @ApiPropertyOptional({
    description: 'Updated by',
    example: 'admin',
  })
  @Expose()
  updatedBy?: string;

  @ApiPropertyOptional({
    description: 'Permissions in this group',
    type: [PermissionSummaryDto],
  })
  @Expose()
  @Type(() => PermissionSummaryDto)
  permissions?: PermissionSummaryDto[];

  @Exclude()
  deletedAt?: Date;

  @Exclude()
  deletedBy?: string;

  @Exclude()
  metadata?: Record<string, any>;
}
