import { IsString, IsUUID, <PERSON><PERSON>ength } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateTeamDto {
  @ApiProperty({
    description: 'Name of the team',
    example: 'Development Team',
    maxLength: 255,
  })
  @IsString()
  @MaxLength(255)
  name: string;

  @ApiProperty({
    description: 'Department ID that this team belongs to',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  departmentId: string;
}
