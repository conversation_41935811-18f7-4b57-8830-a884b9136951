import { IsUUID, IsArray, IsOptional } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class BulkAssignPermissionsDto {
  @ApiProperty({
    description: 'Role ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  roleId: string;

  @ApiProperty({
    description: 'Array of permission IDs to assign',
    example: ['123e4567-e89b-12d3-a456-************', '456e7890-e89b-12d3-a456-************'],
    type: [String],
  })
  @IsArray()
  @IsUUID(4, { each: true })
  permissionIds: string[];

  @ApiPropertyOptional({
    description: 'Department ID (optional context)',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsOptional()
  @IsUUID()
  departmentId?: string;

  @ApiPropertyOptional({
    description: 'Team ID (optional context)',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsOptional()
  @IsUUID()
  teamId?: string;
}
