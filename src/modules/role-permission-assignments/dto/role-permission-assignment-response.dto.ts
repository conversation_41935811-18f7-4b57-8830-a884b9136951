import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Exclude, Expose, Type } from 'class-transformer';

class RoleSummaryDto {
  @Expose()
  id: string;

  @Expose()
  name: string;

  @Expose()
  displayName: string;
}

class PermissionSummaryDto {
  @Expose()
  id: string;

  @Expose()
  action: string;

  @Expose()
  description?: string;
}

class DepartmentSummaryDto {
  @Expose()
  id: string;

  @Expose()
  name: string;
}

class TeamSummaryDto {
  @Expose()
  id: string;

  @Expose()
  name: string;
}

export class RolePermissionAssignmentResponseDto {
  @ApiProperty({
    description: 'Assignment ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @Expose()
  id: string;

  @ApiProperty({
    description: 'Role ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @Expose()
  roleId: string;

  @ApiProperty({
    description: 'Permission ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @Expose()
  permissionId: string;

  @ApiPropertyOptional({
    description: 'Department ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @Expose()
  departmentId?: string;

  @ApiPropertyOptional({
    description: 'Team ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @Expose()
  teamId?: string;

  @ApiProperty({
    description: 'Creation date',
    example: '2023-01-01T00:00:00.000Z',
  })
  @Expose()
  createdAt: Date;

  @ApiProperty({
    description: 'Last update date',
    example: '2023-01-01T00:00:00.000Z',
  })
  @Expose()
  updatedAt: Date;

  @ApiPropertyOptional({
    description: 'Created by',
    example: 'admin',
  })
  @Expose()
  createdBy?: string;

  @ApiPropertyOptional({
    description: 'Updated by',
    example: 'admin',
  })
  @Expose()
  updatedBy?: string;

  @ApiPropertyOptional({
    description: 'Role information',
    type: RoleSummaryDto,
  })
  @Expose()
  @Type(() => RoleSummaryDto)
  role?: RoleSummaryDto;

  @ApiPropertyOptional({
    description: 'Permission information',
    type: PermissionSummaryDto,
  })
  @Expose()
  @Type(() => PermissionSummaryDto)
  permission?: PermissionSummaryDto;

  @ApiPropertyOptional({
    description: 'Department information',
    type: DepartmentSummaryDto,
  })
  @Expose()
  @Type(() => DepartmentSummaryDto)
  department?: DepartmentSummaryDto;

  @ApiPropertyOptional({
    description: 'Team information',
    type: TeamSummaryDto,
  })
  @Expose()
  @Type(() => TeamSummaryDto)
  team?: TeamSummaryDto;

  @Exclude()
  deletedAt?: Date;

  @Exclude()
  deletedBy?: string;

  @Exclude()
  metadata?: Record<string, any>;
}
