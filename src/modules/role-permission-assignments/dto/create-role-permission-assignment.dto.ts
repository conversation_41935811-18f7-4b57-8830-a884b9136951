import { IsUUID, IsOptional } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreateRolePermissionAssignmentDto {
  @ApiProperty({
    description: 'Role ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  roleId: string;

  @ApiProperty({
    description: 'Permission ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  permissionId: string;

  @ApiPropertyOptional({
    description: 'Department ID (optional context)',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsOptional()
  @IsUUID()
  departmentId?: string;

  @ApiPropertyOptional({
    description: 'Team ID (optional context)',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsOptional()
  @IsUUID()
  teamId?: string;
}
