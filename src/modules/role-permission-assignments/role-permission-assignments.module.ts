import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { RolePermissionAssignment } from '@/entities/role-permission-assignment.entity';
import { Role } from '@/entities/role.entity';
import { Permission } from '@/entities/permission.entity';
import { Department } from '@/entities/department.entity';
import { Team } from '@/entities/team.entity';
import { RolePermissionAssignmentsService } from './role-permission-assignments.service';
import { RolePermissionAssignmentsController } from './role-permission-assignments.controller';
import { CommonServicesModule } from '@/common/services/common-services.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([RolePermissionAssignment, Role, Permission, Department, Team]),
    CommonServicesModule,
  ],
  controllers: [RolePermissionAssignmentsController],
  providers: [RolePermissionAssignmentsService],
  exports: [RolePermissionAssignmentsService],
})
export class RolePermissionAssignmentsModule {}
