import {
  <PERSON>,
  Get,
  Query,
  Param,
  HttpStatus,
  ParseU<PERSON><PERSON>ip<PERSON>,
  UseGuards,
  ParseIntPipe,
  DefaultValuePipe,
  ParseEnumPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { AuditLogService, AuditLogQueryDto } from '@/common/services/audit-log.service';
import { AuditAction, AuditStatus } from '@/entities/audit-log.entity';
import { JwtAuthGuard } from '@/common/guards/jwt-auth.guard';
import { RolesGuard } from '@/common/guards/roles.guard';
import { PermissionsGuard } from '@/common/guards/permissions.guard';
import { Roles } from '@/common/decorators/roles.decorator';
import { Permissions } from '@/common/decorators/permissions.decorator';
import { CurrentUser } from '@/common/decorators/current-user.decorator';
import { RoleType } from '@/common/constants/role.constant';
import { PermissionsType } from '@/common/constants/permissions.constant';

@ApiTags('Audit Logs')
@Controller('audit-logs')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class AuditLogController {
  constructor(private readonly auditLogService: AuditLogService) {}

  @Get()
  @UseGuards(PermissionsGuard)
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER)
  @Permissions(PermissionsType.VIEW)
  @ApiOperation({ summary: 'Query audit logs' })
  @ApiQuery({ name: 'entityType', required: false, type: String, description: 'Entity type filter' })
  @ApiQuery({ name: 'entityId', required: false, type: String, description: 'Entity ID filter' })
  @ApiQuery({ name: 'action', required: false, enum: AuditAction, description: 'Action filter' })
  @ApiQuery({ name: 'userId', required: false, type: String, description: 'User ID filter' })
  @ApiQuery({ name: 'status', required: false, enum: AuditStatus, description: 'Status filter' })
  @ApiQuery({ name: 'startDate', required: false, type: String, description: 'Start date (ISO string)' })
  @ApiQuery({ name: 'endDate', required: false, type: String, description: 'End date (ISO string)' })
  @ApiQuery({ name: 'ipAddress', required: false, type: String, description: 'IP address filter' })
  @ApiQuery({ name: 'riskLevel', required: false, type: String, description: 'Risk level filter' })
  @ApiQuery({ name: 'isSensitive', required: false, type: Boolean, description: 'Sensitive operations filter' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Items per page' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Audit logs retrieved successfully',
  })
  async queryAuditLogs(
    @Query('entityType') entityType?: string,
    @Query('entityId') entityId?: string,
    @Query('action') action?: AuditAction,
    @Query('userId') userId?: string,
    @Query('status') status?: AuditStatus,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('ipAddress') ipAddress?: string,
    @Query('riskLevel') riskLevel?: string,
    @Query('isSensitive') isSensitive?: boolean,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number = 1,
    @Query('limit', new DefaultValuePipe(20), ParseIntPipe) limit: number = 20,
  ) {
    const queryDto: AuditLogQueryDto = {
      entityType,
      entityId,
      action,
      userId,
      status,
      startDate: startDate ? new Date(startDate) : undefined,
      endDate: endDate ? new Date(endDate) : undefined,
      ipAddress,
      riskLevel,
      isSensitive,
      page,
      limit,
    };

    return await this.auditLogService.queryAuditLogs(queryDto);
  }

  @Get('my-activities')
  @ApiOperation({ summary: 'Get current user audit logs' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Items per page' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'User audit logs retrieved successfully',
  })
  async getMyActivities(
    @CurrentUser() currentUser: any,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number = 1,
    @Query('limit', new DefaultValuePipe(20), ParseIntPipe) limit: number = 20,
  ) {
    const queryDto: AuditLogQueryDto = {
      userId: currentUser.sub,
      page,
      limit,
    };

    return await this.auditLogService.queryAuditLogs(queryDto);
  }

  @Get('statistics')
  @UseGuards(PermissionsGuard)
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER)
  @Permissions(PermissionsType.VIEW)
  @ApiOperation({ summary: 'Get audit statistics' })
  @ApiQuery({ name: 'startDate', required: false, type: String, description: 'Start date (ISO string)' })
  @ApiQuery({ name: 'endDate', required: false, type: String, description: 'End date (ISO string)' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Audit statistics retrieved successfully',
  })
  async getAuditStatistics(
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    return await this.auditLogService.getAuditStatistics(
      startDate ? new Date(startDate) : undefined,
      endDate ? new Date(endDate) : undefined,
    );
  }

  @Get('entity/:entityType/:entityId')
  @UseGuards(PermissionsGuard)
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER)
  @Permissions(PermissionsType.VIEW)
  @ApiOperation({ summary: 'Get audit logs for specific entity' })
  @ApiParam({ name: 'entityType', description: 'Entity type', type: 'string' })
  @ApiParam({ name: 'entityId', description: 'Entity ID', type: 'string' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Items per page' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Entity audit logs retrieved successfully',
  })
  async getEntityAuditLogs(
    @Param('entityType') entityType: string,
    @Param('entityId') entityId: string,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number = 1,
    @Query('limit', new DefaultValuePipe(20), ParseIntPipe) limit: number = 20,
  ) {
    const queryDto: AuditLogQueryDto = {
      entityType,
      entityId,
      page,
      limit,
    };

    return await this.auditLogService.queryAuditLogs(queryDto);
  }

  @Get('user/:userId')
  @UseGuards(PermissionsGuard)
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER)
  @Permissions(PermissionsType.VIEW)
  @ApiOperation({ summary: 'Get audit logs for specific user' })
  @ApiParam({ name: 'userId', description: 'User ID', type: 'string' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Items per page' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'User audit logs retrieved successfully',
  })
  async getUserAuditLogs(
    @Param('userId', ParseUUIDPipe) userId: string,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number = 1,
    @Query('limit', new DefaultValuePipe(20), ParseIntPipe) limit: number = 20,
  ) {
    const queryDto: AuditLogQueryDto = {
      userId,
      page,
      limit,
    };

    return await this.auditLogService.queryAuditLogs(queryDto);
  }

  @Get('sensitive')
  @UseGuards(PermissionsGuard)
  @Roles(RoleType.SUPER_ADMIN)
  @Permissions(PermissionsType.VIEW)
  @ApiOperation({ summary: 'Get sensitive audit logs' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Items per page' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Sensitive audit logs retrieved successfully',
  })
  async getSensitiveAuditLogs(
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number = 1,
    @Query('limit', new DefaultValuePipe(20), ParseIntPipe) limit: number = 20,
  ) {
    const queryDto: AuditLogQueryDto = {
      isSensitive: true,
      page,
      limit,
    };

    return await this.auditLogService.queryAuditLogs(queryDto);
  }

  @Get('high-risk')
  @UseGuards(PermissionsGuard)
  @Roles(RoleType.SUPER_ADMIN)
  @Permissions(PermissionsType.VIEW)
  @ApiOperation({ summary: 'Get high-risk audit logs' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Items per page' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'High-risk audit logs retrieved successfully',
  })
  async getHighRiskAuditLogs(
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number = 1,
    @Query('limit', new DefaultValuePipe(20), ParseIntPipe) limit: number = 20,
  ) {
    const queryDto: AuditLogQueryDto = {
      riskLevel: 'high',
      page,
      limit,
    };

    return await this.auditLogService.queryAuditLogs(queryDto);
  }

  @Get('failed-operations')
  @UseGuards(PermissionsGuard)
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER)
  @Permissions(PermissionsType.VIEW)
  @ApiOperation({ summary: 'Get failed operations audit logs' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Items per page' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Failed operations audit logs retrieved successfully',
  })
  async getFailedOperations(
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number = 1,
    @Query('limit', new DefaultValuePipe(20), ParseIntPipe) limit: number = 20,
  ) {
    const queryDto: AuditLogQueryDto = {
      status: AuditStatus.FAILED,
      page,
      limit,
    };

    return await this.auditLogService.queryAuditLogs(queryDto);
  }
}
