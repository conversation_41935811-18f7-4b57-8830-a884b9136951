import {
  Controller,
  Post,
  Get,
  Delete,
  Param,
  Query,
  UseInterceptors,
  UploadedFile,
  UploadedFiles,
  HttpStatus,
  UseGuards,
  BadRequestException,
  NotFoundException,
  Res,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiConsumes,
  ApiParam,
  ApiQuery,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { FileInterceptor, FilesInterceptor } from '@nestjs/platform-express';
import { Response } from 'express';
import { diskStorage } from 'multer';
import { extname } from 'path';
import { UploadService, UploadedFile as CustomUploadedFile } from './upload.service';
import { JwtAuthGuard } from '@/common/guards/jwt-auth.guard';
import { RolesGuard } from '@/common/guards/roles.guard';
import { PermissionsGuard } from '@/common/guards/permissions.guard';
import { Roles } from '@/common/decorators/roles.decorator';
import { Permissions } from '@/common/decorators/permissions.decorator';
import { CurrentUser } from '@/common/decorators/current-user.decorator';
import { RoleType } from '@/common/constants/role.constant';
import { PermissionsType } from '@/common/constants/permissions.constant';
import { CustomLoggerService } from '@/common/logger/logger.service';

@ApiTags('Upload')
@Controller('upload')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class UploadController {
  constructor(
    private readonly uploadService: UploadService,
    private readonly logger: CustomLoggerService,
  ) {}

  @Post('image')
  @UseGuards(PermissionsGuard)
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER, RoleType.EMPLOYEE)
  @Permissions(PermissionsType.CREATE)
  @UseInterceptors(
    FileInterceptor('file', {
      storage: diskStorage({
        destination: './uploads/temp',
        filename: (req, file, cb) => {
          const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
          cb(null, file.fieldname + '-' + uniqueSuffix + extname(file.originalname));
        },
      }),
      fileFilter: (req, file, cb) => {
        if (!file.mimetype.match(/\/(jpg|jpeg|png|gif)$/)) {
          return cb(new BadRequestException('Only image files are allowed!'), false);
        }
        cb(null, true);
      },
      limits: {
        fileSize: 10 * 1024 * 1024, // 10MB
      },
    }),
  )
  @ApiOperation({ summary: 'Upload a single image' })
  @ApiConsumes('multipart/form-data')
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Image uploaded successfully',
    schema: {
      type: 'object',
      properties: {
        filename: { type: 'string' },
        originalName: { type: 'string' },
        url: { type: 'string' },
        size: { type: 'number' },
        mimetype: { type: 'string' },
        uploadedAt: { type: 'string', format: 'date-time' },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid file or file too large',
  })
  async uploadImage(
    @UploadedFile() file: Express.Multer.File,
    @CurrentUser() currentUser: any,
  ) {
    if (!file) {
      throw new BadRequestException('No file uploaded');
    }

    this.logger.info(
      `Image upload initiated by user ${currentUser.username}`,
      'UploadController',
      { userId: currentUser.sub, filename: file.originalname }
    );

    const customFile: CustomUploadedFile = {
      fieldname: file.fieldname,
      originalname: file.originalname,
      encoding: file.encoding,
      mimetype: file.mimetype,
      size: file.size,
      destination: file.destination,
      filename: file.filename,
      path: file.path,
    };

    return await this.uploadService.uploadFile(customFile, 'images');
  }

  @Post('images')
  @UseGuards(PermissionsGuard)
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER, RoleType.EMPLOYEE)
  @Permissions(PermissionsType.CREATE)
  @UseInterceptors(
    FilesInterceptor('files', 10, {
      storage: diskStorage({
        destination: './uploads/temp',
        filename: (req, file, cb) => {
          const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
          cb(null, file.fieldname + '-' + uniqueSuffix + extname(file.originalname));
        },
      }),
      fileFilter: (req, file, cb) => {
        if (!file.mimetype.match(/\/(jpg|jpeg|png|gif)$/)) {
          return cb(new BadRequestException('Only image files are allowed!'), false);
        }
        cb(null, true);
      },
      limits: {
        fileSize: 10 * 1024 * 1024, // 10MB per file
        files: 10, // Maximum 10 files
      },
    }),
  )
  @ApiOperation({ summary: 'Upload multiple images' })
  @ApiConsumes('multipart/form-data')
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Images uploaded successfully',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          filename: { type: 'string' },
          originalName: { type: 'string' },
          url: { type: 'string' },
          size: { type: 'number' },
          mimetype: { type: 'string' },
          uploadedAt: { type: 'string', format: 'date-time' },
        },
      },
    },
  })
  async uploadImages(
    @UploadedFiles() files: Express.Multer.File[],
    @CurrentUser() currentUser: any,
  ) {
    if (!files || files.length === 0) {
      throw new BadRequestException('No files uploaded');
    }

    this.logger.info(
      `Multiple images upload initiated by user ${currentUser.username}`,
      'UploadController',
      { userId: currentUser.sub, fileCount: files.length }
    );

    const customFiles: CustomUploadedFile[] = files.map(file => ({
      fieldname: file.fieldname,
      originalname: file.originalname,
      encoding: file.encoding,
      mimetype: file.mimetype,
      size: file.size,
      destination: file.destination,
      filename: file.filename,
      path: file.path,
    }));

    return await this.uploadService.uploadFiles(customFiles, 'images');
  }

  @Get('image/:filename')
  @ApiOperation({ summary: 'Get uploaded image' })
  @ApiParam({ name: 'filename', description: 'Image filename', type: 'string' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Image retrieved successfully',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Image not found',
  })
  async getImage(@Param('filename') filename: string, @Res() res: Response) {
    const fileInfo = await this.uploadService.getFileInfo(filename, 'images');
    
    if (!fileInfo.exists) {
      throw new NotFoundException('Image not found');
    }

    return res.sendFile(fileInfo.path, { root: '/' });
  }

  @Delete('image/:filename')
  @UseGuards(PermissionsGuard)
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER)
  @Permissions(PermissionsType.DELETE)
  @ApiOperation({ summary: 'Delete uploaded image' })
  @ApiParam({ name: 'filename', description: 'Image filename', type: 'string' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Image deleted successfully',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Image not found',
  })
  async deleteImage(
    @Param('filename') filename: string,
    @CurrentUser() currentUser: any,
  ) {
    this.logger.info(
      `Image deletion initiated by user ${currentUser.username}`,
      'UploadController',
      { userId: currentUser.sub, filename }
    );

    await this.uploadService.deleteFile(filename, 'images');
    return { message: 'Image deleted successfully' };
  }

  @Get('info/:filename')
  @UseGuards(PermissionsGuard)
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER)
  @Permissions(PermissionsType.VIEW)
  @ApiOperation({ summary: 'Get file information' })
  @ApiParam({ name: 'filename', description: 'File name', type: 'string' })
  @ApiQuery({ name: 'category', required: false, description: 'File category', type: 'string' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'File information retrieved successfully',
  })
  async getFileInfo(
    @Param('filename') filename: string,
    @Query('category') category: string = 'images',
  ) {
    const fileInfo = await this.uploadService.getFileInfo(filename, category);
    
    if (!fileInfo.exists) {
      throw new NotFoundException('File not found');
    }

    return {
      filename,
      category,
      size: fileInfo.size,
      exists: fileInfo.exists,
      lastModified: fileInfo.stats?.mtime,
      created: fileInfo.stats?.birthtime,
    };
  }

  @Post('cleanup')
  @UseGuards(PermissionsGuard)
  @Roles(RoleType.SUPER_ADMIN)
  @Permissions(PermissionsType.DELETE)
  @ApiOperation({ summary: 'Cleanup old temporary files' })
  @ApiQuery({ name: 'hours', required: false, description: 'Files older than X hours', type: 'number' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Cleanup completed successfully',
  })
  async cleanupTempFiles(
    @Query('hours') hours: number = 24,
    @CurrentUser() currentUser: any,
  ) {
    this.logger.info(
      `Temporary files cleanup initiated by user ${currentUser.username}`,
      'UploadController',
      { userId: currentUser.sub, hours }
    );

    await this.uploadService.cleanupTempFiles(hours);
    return { message: `Cleanup completed for files older than ${hours} hours` };
  }
}
