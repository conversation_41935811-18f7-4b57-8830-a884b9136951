import { Injectable, BadRequestException, InternalServerErrorException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { CustomLoggerService } from '@/common/logger/logger.service';
import * as fs from 'fs';
import * as path from 'path';
import { v4 as uuidv4 } from 'uuid';

export interface UploadedFile {
  fieldname: string;
  originalname: string;
  encoding: string;
  mimetype: string;
  size: number;
  destination: string;
  filename: string;
  path: string;
  buffer?: Buffer;
}

export interface UploadResult {
  filename: string;
  originalName: string;
  path: string;
  url: string;
  size: number;
  mimetype: string;
  uploadedAt: Date;
}

@Injectable()
export class UploadService {
  private readonly uploadPath: string;
  private readonly maxFileSize: number;
  private readonly allowedTypes: string[];
  private readonly baseUrl: string;

  constructor(
    private readonly configService: ConfigService,
    private readonly logger: CustomLoggerService,
  ) {
    this.uploadPath = this.configService.get('upload.destination', './uploads');
    this.maxFileSize = this.configService.get('upload.maxSize', 10485760); // 10MB
    this.allowedTypes = this.configService.get('upload.allowedTypes', ['image/jpeg', 'image/png']);
    this.baseUrl = this.configService.get('app.baseUrl', 'http://localhost:3000');
    
    // Ensure upload directory exists
    this.ensureUploadDirectory();
  }

  /**
   * Ensure upload directory exists
   */
  private ensureUploadDirectory(): void {
    try {
      if (!fs.existsSync(this.uploadPath)) {
        fs.mkdirSync(this.uploadPath, { recursive: true });
        this.logger.info(`Upload directory created: ${this.uploadPath}`, 'UploadService');
      }

      // Create subdirectories for different file types
      const subdirs = ['images', 'documents', 'temp'];
      subdirs.forEach(subdir => {
        const subdirPath = path.join(this.uploadPath, subdir);
        if (!fs.existsSync(subdirPath)) {
          fs.mkdirSync(subdirPath, { recursive: true });
        }
      });
    } catch (error) {
      this.logger.error(
        `Failed to create upload directory: ${error.message}`,
        error.stack,
        'UploadService'
      );
      throw new InternalServerErrorException('Failed to initialize upload directory');
    }
  }

  /**
   * Validate uploaded file
   */
  private validateFile(file: UploadedFile): void {
    // Check file size
    if (file.size > this.maxFileSize) {
      throw new BadRequestException(
        `File size exceeds maximum allowed size of ${this.maxFileSize / 1024 / 1024}MB`
      );
    }

    // Check file type
    if (!this.allowedTypes.includes(file.mimetype)) {
      throw new BadRequestException(
        `File type ${file.mimetype} is not allowed. Allowed types: ${this.allowedTypes.join(', ')}`
      );
    }

    // Check if file has content
    if (file.size === 0) {
      throw new BadRequestException('File is empty');
    }
  }

  /**
   * Generate unique filename
   */
  private generateFilename(originalName: string): string {
    const ext = path.extname(originalName);
    const name = path.basename(originalName, ext);
    const timestamp = Date.now();
    const uuid = uuidv4().split('-')[0];
    return `${name}_${timestamp}_${uuid}${ext}`;
  }

  /**
   * Get file category based on mimetype
   */
  private getFileCategory(mimetype: string): string {
    if (mimetype.startsWith('image/')) return 'images';
    if (mimetype.includes('pdf') || mimetype.includes('document')) return 'documents';
    return 'temp';
  }

  /**
   * Upload single file
   */
  async uploadFile(file: UploadedFile, category?: string): Promise<UploadResult> {
    try {
      this.validateFile(file);

      const fileCategory = category || this.getFileCategory(file.mimetype);
      const filename = this.generateFilename(file.originalname);
      const destinationPath = path.join(this.uploadPath, fileCategory);
      const filePath = path.join(destinationPath, filename);

      // Ensure category directory exists
      if (!fs.existsSync(destinationPath)) {
        fs.mkdirSync(destinationPath, { recursive: true });
      }

      // Write file to disk
      if (file.buffer) {
        fs.writeFileSync(filePath, file.buffer);
      } else if (file.path) {
        fs.copyFileSync(file.path, filePath);
        // Remove temporary file
        fs.unlinkSync(file.path);
      } else {
        throw new BadRequestException('Invalid file data');
      }

      const result: UploadResult = {
        filename,
        originalName: file.originalname,
        path: filePath,
        url: `${this.baseUrl}/uploads/${fileCategory}/${filename}`,
        size: file.size,
        mimetype: file.mimetype,
        uploadedAt: new Date(),
      };

      this.logger.info(
        `File uploaded successfully: ${file.originalname} -> ${filename}`,
        'UploadService',
        {
          originalName: file.originalname,
          filename,
          size: file.size,
          mimetype: file.mimetype,
          category: fileCategory,
        }
      );

      return result;
    } catch (error) {
      this.logger.error(
        `File upload failed: ${error.message}`,
        error.stack,
        'UploadService',
        { originalName: file.originalname, size: file.size }
      );
      throw error;
    }
  }

  /**
   * Upload multiple files
   */
  async uploadFiles(files: UploadedFile[], category?: string): Promise<UploadResult[]> {
    const results: UploadResult[] = [];
    const errors: string[] = [];

    for (const file of files) {
      try {
        const result = await this.uploadFile(file, category);
        results.push(result);
      } catch (error) {
        errors.push(`${file.originalname}: ${error.message}`);
      }
    }

    if (errors.length > 0) {
      this.logger.warn(
        `Some files failed to upload: ${errors.join(', ')}`,
        'UploadService'
      );
    }

    return results;
  }

  /**
   * Delete file
   */
  async deleteFile(filename: string, category: string = 'images'): Promise<void> {
    try {
      const filePath = path.join(this.uploadPath, category, filename);
      
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
        this.logger.info(
          `File deleted successfully: ${filename}`,
          'UploadService',
          { filename, category }
        );
      } else {
        this.logger.warn(
          `File not found for deletion: ${filename}`,
          'UploadService',
          { filename, category }
        );
      }
    } catch (error) {
      this.logger.error(
        `Failed to delete file: ${error.message}`,
        error.stack,
        'UploadService',
        { filename, category }
      );
      throw new InternalServerErrorException('Failed to delete file');
    }
  }

  /**
   * Get file info
   */
  async getFileInfo(filename: string, category: string = 'images'): Promise<{
    exists: boolean;
    path?: string;
    size?: number;
    stats?: fs.Stats;
  }> {
    try {
      const filePath = path.join(this.uploadPath, category, filename);
      
      if (fs.existsSync(filePath)) {
        const stats = fs.statSync(filePath);
        return {
          exists: true,
          path: filePath,
          size: stats.size,
          stats,
        };
      }

      return { exists: false };
    } catch (error) {
      this.logger.error(
        `Failed to get file info: ${error.message}`,
        error.stack,
        'UploadService',
        { filename, category }
      );
      return { exists: false };
    }
  }

  /**
   * Clean up old temporary files
   */
  async cleanupTempFiles(olderThanHours: number = 24): Promise<void> {
    try {
      const tempPath = path.join(this.uploadPath, 'temp');
      if (!fs.existsSync(tempPath)) return;

      const files = fs.readdirSync(tempPath);
      const cutoffTime = Date.now() - (olderThanHours * 60 * 60 * 1000);
      let deletedCount = 0;

      for (const file of files) {
        const filePath = path.join(tempPath, file);
        const stats = fs.statSync(filePath);
        
        if (stats.mtime.getTime() < cutoffTime) {
          fs.unlinkSync(filePath);
          deletedCount++;
        }
      }

      this.logger.info(
        `Cleaned up ${deletedCount} temporary files older than ${olderThanHours} hours`,
        'UploadService',
        { deletedCount, olderThanHours }
      );
    } catch (error) {
      this.logger.error(
        `Failed to cleanup temporary files: ${error.message}`,
        error.stack,
        'UploadService'
      );
    }
  }
}
