import {
  <PERSON>,
  Get,
  Param,
  Query,
  HttpStatus,
  ParseU<PERSON><PERSON>ip<PERSON>,
  UseGuards,
  Request,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { RolesService } from './roles.service';
import { PaginationDto, PaginatedResult } from '@/common/dto/pagination.dto';
import { Role } from '@/entities/role.entity';
import { AssignableRolesResponseDto } from './dto/assignable-roles-response.dto';
import { JwtAuthGuard } from '@/common/guards/jwt-auth.guard';
import { RolesGuard } from '@/common/guards/roles.guard';
import { PermissionsGuard } from '@/common/guards/permissions.guard';
import { Roles } from '@/common/decorators/roles.decorator';
import { Permissions } from '@/common/decorators/permissions.decorator';
import { RoleType, PermissionsType } from '@/common/constants/permissions.type';

@ApiTags('Roles')
@Controller('roles')
export class RolesController {
  constructor(private readonly rolesService: RolesService) {}

  @Get()
  @ApiOperation({ summary: 'Get all roles with pagination and search' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Roles retrieved successfully',
  })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Items per page' })
  @ApiQuery({ name: 'search', required: false, type: String, description: 'Search term' })
  @ApiQuery({ name: 'sortBy', required: false, type: String, description: 'Sort field' })
  @ApiQuery({ name: 'sortOrder', required: false, enum: ['ASC', 'DESC'], description: 'Sort order' })
  @UseGuards(JwtAuthGuard, RolesGuard, PermissionsGuard)
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER)
  @Permissions(PermissionsType.VIEW)
  async findAll(@Query() paginationDto: PaginationDto): Promise<PaginatedResult<Role>> {
    return await this.rolesService.findAll(paginationDto);
  }

  @Get('system')
  @ApiOperation({ summary: 'Get system roles' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'System roles retrieved successfully',
  })
  @UseGuards(JwtAuthGuard, RolesGuard, PermissionsGuard)
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER)
  @Permissions(PermissionsType.VIEW)
  async findSystemRoles(): Promise<Role[]> {
    return await this.rolesService.findSystemRoles();
  }

  @Get('non-system')
  @ApiOperation({ summary: 'Get non-system roles' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Non-system roles retrieved successfully',
  })
  @UseGuards(JwtAuthGuard, RolesGuard, PermissionsGuard)
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER)
  @Permissions(PermissionsType.VIEW)
  async findNonSystemRoles(): Promise<Role[]> {
    return await this.rolesService.findNonSystemRoles();
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get role by ID' })
  @ApiParam({ name: 'id', description: 'Role ID', type: 'string' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Role retrieved successfully',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Role not found',
  })
  @UseGuards(JwtAuthGuard, RolesGuard, PermissionsGuard)
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER)
  @Permissions(PermissionsType.UPDATE)
  async findOne(@Param('id', ParseUUIDPipe) id: string): Promise<Role> {
    return await this.rolesService.findOne(id);
  }

  @Get('assignable')
  @ApiOperation({ summary: 'Get roles that current user can assign' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'List of assignable roles retrieved successfully',
    type: AssignableRolesResponseDto,
  })
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard, RolesGuard, PermissionsGuard)
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER)
  @Permissions(PermissionsType.VIEW)
  async getAssignableRoles(@Request() req: any): Promise<AssignableRolesResponseDto> {
    const result = await this.rolesService.getAssignableRoles(req.user.id);

    return {
      roles: result.roles.map(role => ({
        id: role.id,
        name: role.name,
        displayName: role.displayName,
        level: role.level,
        description: role.description,
        isSystem: role.isSystem,
      })),
      currentUserRole: {
        name: result.currentUserRole.name,
        level: result.currentUserRole.level,
        displayName: result.currentUserRole.displayName,
      },
    };
  }
}
