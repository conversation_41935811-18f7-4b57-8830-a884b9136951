import { Injectable, ConflictException, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from '@/entities/user.entity';
import { CustomLoggerService } from '@/common/logger/logger.service';

@Injectable()
export class AffiliateService {
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly logger: CustomLoggerService,
  ) {}

  /**
   * Generate a unique affiliate code for a user
   */
  async generateAffiliateCode(userId: string, customCode?: string): Promise<string> {
    const user = await this.userRepository.findOne({ where: { id: userId } });
    if (!user) {
      throw new NotFoundException('User not found');
    }

    let affiliateCode: string;

    if (customCode) {
      // Check if custom code is already taken
      const existingUser = await this.userRepository.findOne({
        where: { affiliateCode: customCode },
      });
      if (existingUser) {
        throw new ConflictException('Affiliate code already exists');
      }
      affiliateCode = customCode;
    } else {
      // Generate automatic code based on username and timestamp
      const baseCode = `AFF-${user.username.toUpperCase()}-${new Date().getFullYear()}`;
      let counter = 1;
      affiliateCode = baseCode;

      // Ensure uniqueness
      while (await this.userRepository.findOne({ where: { affiliateCode } })) {
        affiliateCode = `${baseCode}-${counter}`;
        counter++;
      }
    }

    // Update user with affiliate code
    user.affiliateCode = affiliateCode;
    user.affiliateCodeAssignedAt = new Date();
    user.affiliateCodeActive = true;

    await this.userRepository.save(user);

    this.logger.info(
      `Affiliate code generated for user ${user.username}: ${affiliateCode}`,
      'AffiliateService',
      { userId, affiliateCode, customCode: !!customCode }
    );

    return affiliateCode;
  }

  /**
   * Find user by affiliate code
   */
  async findUserByAffiliateCode(affiliateCode: string): Promise<User | null> {
    return await this.userRepository.findOne({
      where: { affiliateCode, affiliateCodeActive: true },
    });
  }

  /**
   * Get affiliate statistics for a user
   */
  async getAffiliateStats(userId: string): Promise<{
    totalReferrals: number;
    activeReferrals: number;
    affiliateCode: string | null;
    isActive: boolean;
  }> {
    const user = await this.userRepository.findOne({ where: { id: userId } });
    if (!user) {
      throw new NotFoundException('User not found');
    }

    const totalReferrals = await this.userRepository.count({
      where: { referredBy: userId },
    });

    const activeReferrals = await this.userRepository.count({
      where: { referredBy: userId, deletedAt: null },
    });

    return {
      totalReferrals,
      activeReferrals,
      affiliateCode: user.affiliateCode,
      isActive: user.affiliateCodeActive,
    };
  }

  /**
   * Get list of users referred by an affiliate
   */
  async getReferredUsers(userId: string, page: number = 1, limit: number = 10): Promise<{
    users: User[];
    total: number;
    page: number;
    limit: number;
  }> {
    const [users, total] = await this.userRepository.findAndCount({
      where: { referredBy: userId },
      select: ['id', 'username', 'fullName', 'email', 'createdAt'],
      skip: (page - 1) * limit,
      take: limit,
      order: { createdAt: 'DESC' },
    });

    return {
      users,
      total,
      page,
      limit,
    };
  }

  /**
   * Activate or deactivate affiliate code
   */
  async toggleAffiliateCode(userId: string, active: boolean): Promise<void> {
    const user = await this.userRepository.findOne({ where: { id: userId } });
    if (!user) {
      throw new NotFoundException('User not found');
    }

    if (!user.affiliateCode) {
      throw new ConflictException('User does not have an affiliate code');
    }

    user.affiliateCodeActive = active;
    await this.userRepository.save(user);

    this.logger.info(
      `Affiliate code ${active ? 'activated' : 'deactivated'} for user ${user.username}`,
      'AffiliateService',
      { userId, affiliateCode: user.affiliateCode, active }
    );
  }

  /**
   * Update affiliate code
   */
  async updateAffiliateCode(userId: string, newCode: string): Promise<void> {
    const user = await this.userRepository.findOne({ where: { id: userId } });
    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Check if new code is already taken
    const existingUser = await this.userRepository.findOne({
      where: { affiliateCode: newCode },
    });
    if (existingUser && existingUser.id !== userId) {
      throw new ConflictException('Affiliate code already exists');
    }

    const oldCode = user.affiliateCode;
    user.affiliateCode = newCode;
    await this.userRepository.save(user);

    this.logger.info(
      `Affiliate code updated for user ${user.username}: ${oldCode} -> ${newCode}`,
      'AffiliateService',
      { userId, oldCode, newCode }
    );
  }

  /**
   * Process referral when a new user registers
   */
  async processReferral(newUserId: string, referrerAffiliateCode: string): Promise<void> {
    const referrer = await this.findUserByAffiliateCode(referrerAffiliateCode);
    if (!referrer) {
      this.logger.warn(
        `Invalid affiliate code used during registration: ${referrerAffiliateCode}`,
        'AffiliateService',
        { newUserId, referrerAffiliateCode }
      );
      return;
    }

    // Update new user with referrer information
    await this.userRepository.update(newUserId, {
      referredBy: referrer.id,
    });

    this.logger.info(
      `New user referred by ${referrer.username} (${referrerAffiliateCode})`,
      'AffiliateService',
      { newUserId, referrerId: referrer.id, affiliateCode: referrerAffiliateCode }
    );
  }
}
