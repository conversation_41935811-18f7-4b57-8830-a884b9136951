import { IsString, Is<PERSON>ptional, IsBoolean, <PERSON><PERSON><PERSON>th, <PERSON><PERSON>ength } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class GenerateAffiliateCodeDto {
  @ApiPropertyOptional({
    description: 'Custom affiliate code (optional). If not provided, will be auto-generated',
    example: 'AFF-JOHN-2024',
    minLength: 5,
    maxLength: 50,
  })
  @IsOptional()
  @IsString()
  @MinLength(5)
  @MaxLength(50)
  customCode?: string;
}

export class UpdateAffiliateCodeDto {
  @ApiProperty({
    description: 'New affiliate code',
    example: 'AFF-JOHN-2024-NEW',
    minLength: 5,
    maxLength: 50,
  })
  @IsString()
  @MinLength(5)
  @MaxLength(50)
  newCode: string;
}

export class ToggleAffiliateCodeDto {
  @ApiProperty({
    description: 'Whether to activate or deactivate the affiliate code',
    example: true,
  })
  @IsBoolean()
  active: boolean;
}

export class AffiliateStatsResponseDto {
  @ApiProperty({
    description: 'Total number of referrals',
    example: 15,
  })
  totalReferrals: number;

  @ApiProperty({
    description: 'Number of active referrals',
    example: 12,
  })
  activeReferrals: number;

  @ApiProperty({
    description: 'User affiliate code',
    example: 'AFF-JOHN-2024',
    nullable: true,
  })
  affiliateCode: string | null;

  @ApiProperty({
    description: 'Whether the affiliate code is active',
    example: true,
  })
  isActive: boolean;
}

export class ReferredUserDto {
  @ApiProperty({
    description: 'User ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;

  @ApiProperty({
    description: 'Username',
    example: 'john_doe',
  })
  username: string;

  @ApiProperty({
    description: 'Full name',
    example: 'John Doe',
  })
  fullName: string;

  @ApiProperty({
    description: 'Email address',
    example: '<EMAIL>',
  })
  email: string;

  @ApiProperty({
    description: 'Registration date',
    example: '2024-01-15T10:30:00Z',
  })
  createdAt: Date;
}

export class ReferredUsersResponseDto {
  @ApiProperty({
    description: 'List of referred users',
    type: [ReferredUserDto],
  })
  users: ReferredUserDto[];

  @ApiProperty({
    description: 'Total number of referred users',
    example: 25,
  })
  total: number;

  @ApiProperty({
    description: 'Current page number',
    example: 1,
  })
  page: number;

  @ApiProperty({
    description: 'Number of items per page',
    example: 10,
  })
  limit: number;
}
