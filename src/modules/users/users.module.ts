import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User } from '@/entities/user.entity';
import { UsersService } from './users.service';
import { UsersController } from './users.controller';
import { AffiliateService } from './services/affiliate.service';
import { AffiliateController } from './controllers/affiliate.controller';

@Module({
  imports: [TypeOrmModule.forFeature([User])],
  controllers: [UsersController, AffiliateController],
  providers: [UsersService, AffiliateService],
  exports: [UsersService, AffiliateService],
})
export class UsersModule {}
