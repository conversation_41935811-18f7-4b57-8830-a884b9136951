import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Affiliate } from '@/entities/affiliate.entity';
import { AffiliateUsage } from '@/entities/affiliate-usage.entity';
import { User } from '@/entities/user.entity';
import { AffiliateService } from './services/affiliate.service';
import { AffiliateUsageService } from './services/affiliate-usage.service';
import { AffiliateController } from './controllers/affiliate.controller';
import { AuditLogService } from '@/common/services/audit-log.service';
import { AuditLog } from '@/entities/audit-log.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Affiliate,
      AffiliateUsage,
      User,
      AuditLog,
    ]),
  ],
  controllers: [AffiliateController],
  providers: [
    AffiliateService,
    AffiliateUsageService,
    AuditLogService,
  ],
  exports: [
    AffiliateService,
    AffiliateUsageService,
  ],
})
export class AffiliateModule {}
