import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between } from 'typeorm';
import { AffiliateUsage, AffiliateUsageStatus, AffiliateUsageType } from '@/entities/affiliate-usage.entity';
import { Affiliate } from '@/entities/affiliate.entity';
import { User } from '@/entities/user.entity';
import { CustomLoggerService } from '@/common/logger/logger.service';
import { LoggerIntegrationUtil } from '@/common/utils/logger-integration.util';

export interface TrackUsageDto {
  affiliateCode: string;
  usageType: AffiliateUsageType;
  ipAddress?: string;
  userAgent?: string;
  referrerUrl?: string;
  landingUrl?: string;
  utmSource?: string;
  utmMedium?: string;
  utmCampaign?: string;
  sessionId?: string;
  deviceType?: string;
  browser?: string;
  operatingSystem?: string;
  metadata?: Record<string, any>;
}

export interface UpdateUsageStatusDto {
  status: AffiliateUsageStatus;
  userId?: string;
  commissionAmount?: number;
  transactionValue?: number;
  currency?: string;
  notes?: string;
  metadata?: Record<string, any>;
}

export interface UsageAnalyticsDto {
  affiliateId?: string;
  startDate?: Date;
  endDate?: Date;
  usageType?: AffiliateUsageType;
  status?: AffiliateUsageStatus;
  groupBy?: 'day' | 'week' | 'month' | 'year';
}

@Injectable()
export class AffiliateUsageService {
  private serviceLogger: ReturnType<typeof LoggerIntegrationUtil.createServiceLogger>;

  constructor(
    @InjectRepository(AffiliateUsage)
    private readonly affiliateUsageRepository: Repository<AffiliateUsage>,
    @InjectRepository(Affiliate)
    private readonly affiliateRepository: Repository<Affiliate>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly logger: CustomLoggerService,
  ) {
    this.serviceLogger = LoggerIntegrationUtil.createServiceLogger(this.logger, 'AffiliateUsageService');
  }

  /**
   * Track affiliate code usage
   */
  async trackUsage(trackUsageDto: TrackUsageDto): Promise<AffiliateUsage> {
    this.serviceLogger.logCreate('affiliate usage', trackUsageDto);

    try {
      // Find affiliate by code
      const affiliate = await this.affiliateRepository.findOne({
        where: { code: trackUsageDto.affiliateCode, isActive: true },
      });

      if (!affiliate) {
        this.serviceLogger.logNotFound('affiliate', trackUsageDto.affiliateCode);
        throw new NotFoundException(`Affiliate code ${trackUsageDto.affiliateCode} not found or inactive`);
      }

      // Check if affiliate can be used
      if (!affiliate.canBeUsed) {
        this.logger.warn(
          `Affiliate code cannot be used: ${trackUsageDto.affiliateCode}`,
          'AffiliateUsageService',
          { 
            isExpired: affiliate.isExpired, 
            isUsageLimitReached: affiliate.isUsageLimitReached,
            isActive: affiliate.isActive 
          }
        );
        throw new Error('Affiliate code cannot be used (expired, limit reached, or inactive)');
      }

      // Create usage record
      const usage = this.affiliateUsageRepository.create({
        affiliateId: affiliate.id,
        usageType: trackUsageDto.usageType,
        status: AffiliateUsageStatus.PENDING,
        ipAddress: trackUsageDto.ipAddress,
        userAgent: trackUsageDto.userAgent,
        referrerUrl: trackUsageDto.referrerUrl,
        landingUrl: trackUsageDto.landingUrl,
        utmSource: trackUsageDto.utmSource,
        utmMedium: trackUsageDto.utmMedium,
        utmCampaign: trackUsageDto.utmCampaign,
        sessionId: trackUsageDto.sessionId,
        deviceType: trackUsageDto.deviceType,
        browser: trackUsageDto.browser,
        operatingSystem: trackUsageDto.operatingSystem,
        metadata: trackUsageDto.metadata,
      });

      const savedUsage = await this.affiliateUsageRepository.save(usage);

      // Update affiliate usage count
      await this.affiliateRepository.increment(
        { id: affiliate.id },
        'totalUsageCount',
        1
      );

      this.serviceLogger.logCreateSuccess('affiliate usage', savedUsage.id, {
        affiliateCode: trackUsageDto.affiliateCode,
        usageType: trackUsageDto.usageType,
      });

      return savedUsage;
    } catch (error) {
      this.serviceLogger.logCreateError('affiliate usage', error, trackUsageDto);
      throw error;
    }
  }

  /**
   * Update usage status (e.g., when conversion happens)
   */
  async updateUsageStatus(usageId: string, updateDto: UpdateUsageStatusDto): Promise<AffiliateUsage> {
    this.serviceLogger.logUpdate('affiliate usage', usageId, updateDto);

    try {
      const usage = await this.affiliateUsageRepository.findOne({
        where: { id: usageId },
        relations: ['affiliate'],
      });

      if (!usage) {
        this.serviceLogger.logNotFound('affiliate usage', usageId);
        throw new NotFoundException(`Affiliate usage ${usageId} not found`);
      }

      const oldStatus = usage.status;

      // Update usage record
      Object.assign(usage, {
        status: updateDto.status,
        userId: updateDto.userId,
        commissionAmount: updateDto.commissionAmount,
        transactionValue: updateDto.transactionValue,
        currency: updateDto.currency,
        notes: updateDto.notes,
        metadata: { ...usage.metadata, ...updateDto.metadata },
        isConverted: updateDto.status === AffiliateUsageStatus.SUCCESS,
        convertedAt: updateDto.status === AffiliateUsageStatus.SUCCESS ? new Date() : usage.convertedAt,
      });

      const savedUsage = await this.affiliateUsageRepository.save(usage);

      // Update affiliate statistics if status changed to success
      if (oldStatus !== AffiliateUsageStatus.SUCCESS && updateDto.status === AffiliateUsageStatus.SUCCESS) {
        await this.affiliateRepository.increment(
          { id: usage.affiliateId },
          'successfulReferrals',
          1
        );

        if (updateDto.commissionAmount) {
          await this.affiliateRepository.increment(
            { id: usage.affiliateId },
            'totalCommissionEarned',
            updateDto.commissionAmount
          );
        }
      }

      this.serviceLogger.logUpdateSuccess('affiliate usage', usageId, {
        oldStatus,
        newStatus: updateDto.status,
        isConverted: savedUsage.isConverted,
      });

      return savedUsage;
    } catch (error) {
      this.serviceLogger.logUpdateError('affiliate usage', usageId, error, updateDto);
      throw error;
    }
  }

  /**
   * Get usage analytics
   */
  async getUsageAnalytics(analyticsDto: UsageAnalyticsDto): Promise<any> {
    this.serviceLogger.logFind('usage analytics', analyticsDto);

    try {
      const queryBuilder = this.affiliateUsageRepository
        .createQueryBuilder('usage')
        .leftJoinAndSelect('usage.affiliate', 'affiliate')
        .leftJoinAndSelect('usage.user', 'user');

      // Apply filters
      if (analyticsDto.affiliateId) {
        queryBuilder.andWhere('usage.affiliateId = :affiliateId', { affiliateId: analyticsDto.affiliateId });
      }

      if (analyticsDto.usageType) {
        queryBuilder.andWhere('usage.usageType = :usageType', { usageType: analyticsDto.usageType });
      }

      if (analyticsDto.status) {
        queryBuilder.andWhere('usage.status = :status', { status: analyticsDto.status });
      }

      if (analyticsDto.startDate && analyticsDto.endDate) {
        queryBuilder.andWhere('usage.createdAt BETWEEN :startDate AND :endDate', {
          startDate: analyticsDto.startDate,
          endDate: analyticsDto.endDate,
        });
      }

      // Group by time period if specified
      if (analyticsDto.groupBy) {
        const dateFormat = this.getDateFormat(analyticsDto.groupBy);
        queryBuilder
          .select([
            `DATE_FORMAT(usage.createdAt, '${dateFormat}') as period`,
            'COUNT(*) as totalUsages',
            'SUM(CASE WHEN usage.status = "success" THEN 1 ELSE 0 END) as successfulUsages',
            'SUM(CASE WHEN usage.isConverted = true THEN 1 ELSE 0 END) as conversions',
            'SUM(usage.commissionAmount) as totalCommission',
            'SUM(usage.transactionValue) as totalTransactionValue',
          ])
          .groupBy('period')
          .orderBy('period', 'ASC');

        const results = await queryBuilder.getRawMany();
        
        this.serviceLogger.logFindSuccess('usage analytics', results.length, analyticsDto);
        return results;
      }

      // Get detailed analytics
      const [usages, total] = await queryBuilder.getManyAndCount();

      const analytics = {
        total,
        usages,
        summary: {
          totalUsages: total,
          successfulUsages: usages.filter(u => u.status === AffiliateUsageStatus.SUCCESS).length,
          conversions: usages.filter(u => u.isConverted).length,
          totalCommission: usages.reduce((sum, u) => sum + (u.commissionAmount || 0), 0),
          totalTransactionValue: usages.reduce((sum, u) => sum + (u.transactionValue || 0), 0),
          conversionRate: total > 0 ? (usages.filter(u => u.isConverted).length / total) * 100 : 0,
        },
      };

      this.serviceLogger.logFindSuccess('usage analytics', total, analyticsDto);
      return analytics;
    } catch (error) {
      this.logger.error(
        `Failed to get usage analytics: ${error.message}`,
        error.stack,
        'AffiliateUsageService'
      );
      throw error;
    }
  }

  /**
   * Get usage history for an affiliate
   */
  async getUsageHistory(affiliateId: string, page: number = 1, limit: number = 20): Promise<{
    usages: AffiliateUsage[];
    total: number;
    page: number;
    limit: number;
  }> {
    this.serviceLogger.logFind('usage history', { affiliateId, page, limit });

    try {
      const [usages, total] = await this.affiliateUsageRepository.findAndCount({
        where: { affiliateId },
        relations: ['user'],
        order: { createdAt: 'DESC' },
        skip: (page - 1) * limit,
        take: limit,
      });

      this.serviceLogger.logFindSuccess('usage history', total, { affiliateId, page, limit });

      return {
        usages,
        total,
        page,
        limit,
      };
    } catch (error) {
      this.logger.error(
        `Failed to get usage history: ${error.message}`,
        error.stack,
        'AffiliateUsageService'
      );
      throw error;
    }
  }

  /**
   * Get top performing affiliates
   */
  async getTopPerformingAffiliates(limit: number = 10): Promise<any[]> {
    this.serviceLogger.logFind('top performing affiliates', { limit });

    try {
      const results = await this.affiliateUsageRepository
        .createQueryBuilder('usage')
        .leftJoinAndSelect('usage.affiliate', 'affiliate')
        .leftJoinAndSelect('affiliate.user', 'user')
        .select([
          'affiliate.id',
          'affiliate.code',
          'affiliate.description',
          'user.username',
          'user.fullName',
          'COUNT(*) as totalUsages',
          'SUM(CASE WHEN usage.status = "success" THEN 1 ELSE 0 END) as successfulUsages',
          'SUM(usage.commissionAmount) as totalCommission',
          'AVG(CASE WHEN usage.isConverted = true THEN TIMESTAMPDIFF(SECOND, usage.createdAt, usage.convertedAt) ELSE NULL END) as avgConversionTime',
        ])
        .where('usage.createdAt >= :startDate', { startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) }) // Last 30 days
        .groupBy('affiliate.id')
        .orderBy('successfulUsages', 'DESC')
        .limit(limit)
        .getRawMany();

      this.serviceLogger.logFindSuccess('top performing affiliates', results.length, { limit });
      return results;
    } catch (error) {
      this.logger.error(
        `Failed to get top performing affiliates: ${error.message}`,
        error.stack,
        'AffiliateUsageService'
      );
      throw error;
    }
  }

  private getDateFormat(groupBy: string): string {
    switch (groupBy) {
      case 'day':
        return '%Y-%m-%d';
      case 'week':
        return '%Y-%u';
      case 'month':
        return '%Y-%m';
      case 'year':
        return '%Y';
      default:
        return '%Y-%m-%d';
    }
  }
}
