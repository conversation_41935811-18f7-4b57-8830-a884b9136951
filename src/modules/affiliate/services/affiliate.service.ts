import { Injectable, ConflictException, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Affiliate } from '@/entities/affiliate.entity';
import { User } from '@/entities/user.entity';
import { AffiliateUsage, AffiliateUsageType, AffiliateUsageStatus } from '@/entities/affiliate-usage.entity';
import { CustomLoggerService } from '@/common/logger/logger.service';
import { LoggerIntegrationUtil } from '@/common/utils/logger-integration.util';
import { AuditLogService, AuditLog } from '@/common/services/audit-log.service';
import { AuditAction } from '@/entities/audit-log.entity';

export interface CreateAffiliateDto {
  code?: string;
  description?: string;
  commissionRate?: number;
  category?: string;
  expiresAt?: Date;
  maxUsageLimit?: number;
  metadata?: Record<string, any>;
}

export interface UpdateAffiliateDto {
  description?: string;
  commissionRate?: number;
  category?: string;
  expiresAt?: Date;
  maxUsageLimit?: number;
  metadata?: Record<string, any>;
}

export interface AffiliateStatsDto {
  totalUsageCount: number;
  successfulReferrals: number;
  totalCommissionEarned: number;
  conversionRate: number;
  isActive: boolean;
  isExpired: boolean;
  isUsageLimitReached: boolean;
  canBeUsed: boolean;
}

@Injectable()
export class AffiliateService {
  private serviceLogger: ReturnType<typeof LoggerIntegrationUtil.createServiceLogger>;

  constructor(
    @InjectRepository(Affiliate)
    private readonly affiliateRepository: Repository<Affiliate>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(AffiliateUsage)
    private readonly affiliateUsageRepository: Repository<AffiliateUsage>,
    private readonly logger: CustomLoggerService,
    private readonly auditLogService: AuditLogService,
  ) {
    this.serviceLogger = LoggerIntegrationUtil.createServiceLogger(this.logger, 'AffiliateService');
  }

  /**
   * Create affiliate code for a user
   */
  @AuditLog({
    action: AuditAction.CREATE,
    entityType: 'Affiliate',
    getEntityId: (args) => args[1], // userId
    riskLevel: 'medium',
    isSensitive: true,
  })
  async createAffiliate(userId: string, createDto: CreateAffiliateDto): Promise<Affiliate> {
    this.serviceLogger.logCreate('affiliate', { userId, ...createDto });

    try {
      // Check if user exists
      const user = await this.userRepository.findOne({ where: { id: userId } });
      if (!user) {
        this.serviceLogger.logNotFound('user', userId);
        throw new NotFoundException('User not found');
      }

      // Generate code if not provided
      let code = createDto.code;
      if (!code) {
        code = await this.generateUniqueCode(user.username);
      } else {
        // Check if custom code already exists
        const existingAffiliate = await this.affiliateRepository.findOne({
          where: { code },
        });
        if (existingAffiliate) {
          this.serviceLogger.logConflict('create affiliate', `Code already exists: ${code}`);
          throw new ConflictException('Affiliate code already exists');
        }
      }

      // Create affiliate
      const affiliate = this.affiliateRepository.create({
        code,
        userId,
        description: createDto.description,
        commissionRate: createDto.commissionRate || 0,
        category: createDto.category || 'general',
        expiresAt: createDto.expiresAt,
        maxUsageLimit: createDto.maxUsageLimit,
        metadata: createDto.metadata,
        isActive: true,
        activatedAt: new Date(),
      });

      const savedAffiliate = await this.affiliateRepository.save(affiliate);

      this.serviceLogger.logCreateSuccess('affiliate', savedAffiliate.id, {
        code: savedAffiliate.code,
        userId,
      });

      return savedAffiliate;
    } catch (error) {
      this.serviceLogger.logCreateError('affiliate', error, { userId, ...createDto });
      throw error;
    }
  }

  /**
   * Update affiliate
   */
  @AuditLog({
    action: AuditAction.UPDATE,
    entityType: 'Affiliate',
    getEntityId: (args) => args[0], // affiliateId
    riskLevel: 'medium',
    isSensitive: true,
  })
  async updateAffiliate(affiliateId: string, updateDto: UpdateAffiliateDto): Promise<Affiliate> {
    this.serviceLogger.logUpdate('affiliate', affiliateId, updateDto);

    try {
      const affiliate = await this.affiliateRepository.findOne({
        where: { id: affiliateId },
      });

      if (!affiliate) {
        this.serviceLogger.logNotFound('affiliate', affiliateId);
        throw new NotFoundException('Affiliate not found');
      }

      // Store old values for audit
      const oldValues = { ...affiliate };

      // Update affiliate
      Object.assign(affiliate, updateDto);
      const savedAffiliate = await this.affiliateRepository.save(affiliate);

      // Log audit
      await this.auditLogService.logCrudOperation(
        AuditAction.UPDATE,
        'Affiliate',
        affiliateId,
        undefined,
        oldValues,
        savedAffiliate
      );

      this.serviceLogger.logUpdateSuccess('affiliate', affiliateId, updateDto);

      return savedAffiliate;
    } catch (error) {
      this.serviceLogger.logUpdateError('affiliate', affiliateId, error, updateDto);
      throw error;
    }
  }

  /**
   * Toggle affiliate status
   */
  async toggleAffiliateStatus(affiliateId: string, isActive: boolean): Promise<Affiliate> {
    this.serviceLogger.logUpdate('affiliate status', affiliateId, { isActive });

    try {
      const affiliate = await this.affiliateRepository.findOne({
        where: { id: affiliateId },
      });

      if (!affiliate) {
        this.serviceLogger.logNotFound('affiliate', affiliateId);
        throw new NotFoundException('Affiliate not found');
      }

      const oldValues = { isActive: affiliate.isActive };

      affiliate.isActive = isActive;
      affiliate.activatedAt = isActive ? new Date() : affiliate.activatedAt;
      affiliate.deactivatedAt = !isActive ? new Date() : null;

      const savedAffiliate = await this.affiliateRepository.save(affiliate);

      // Log audit
      await this.auditLogService.logCrudOperation(
        AuditAction.UPDATE,
        'Affiliate',
        affiliateId,
        undefined,
        oldValues,
        { isActive: savedAffiliate.isActive }
      );

      this.serviceLogger.logUpdateSuccess('affiliate status', affiliateId, { isActive });

      return savedAffiliate;
    } catch (error) {
      this.serviceLogger.logUpdateError('affiliate status', affiliateId, error, { isActive });
      throw error;
    }
  }

  /**
   * Find affiliate by code
   */
  async findByCode(code: string): Promise<Affiliate | null> {
    this.serviceLogger.logFind('affiliate by code', { code });

    try {
      const affiliate = await this.affiliateRepository.findOne({
        where: { code },
        relations: ['user'],
      });

      if (affiliate) {
        this.serviceLogger.logFindSuccess('affiliate by code', 1, { code });
      } else {
        this.serviceLogger.logNotFound('affiliate', code);
      }

      return affiliate;
    } catch (error) {
      this.logger.error(
        `Failed to find affiliate by code: ${error.message}`,
        error.stack,
        'AffiliateService'
      );
      throw error;
    }
  }

  /**
   * Get affiliate statistics
   */
  async getAffiliateStats(affiliateId: string): Promise<AffiliateStatsDto> {
    this.serviceLogger.logFind('affiliate stats', { affiliateId });

    try {
      const affiliate = await this.affiliateRepository.findOne({
        where: { id: affiliateId },
      });

      if (!affiliate) {
        this.serviceLogger.logNotFound('affiliate', affiliateId);
        throw new NotFoundException('Affiliate not found');
      }

      const stats: AffiliateStatsDto = {
        totalUsageCount: affiliate.totalUsageCount,
        successfulReferrals: affiliate.successfulReferrals,
        totalCommissionEarned: affiliate.totalCommissionEarned,
        conversionRate: affiliate.conversionRate,
        isActive: affiliate.isActive,
        isExpired: affiliate.isExpired,
        isUsageLimitReached: affiliate.isUsageLimitReached,
        canBeUsed: affiliate.canBeUsed,
      };

      this.serviceLogger.logFindSuccess('affiliate stats', 1, { affiliateId });

      return stats;
    } catch (error) {
      this.logger.error(
        `Failed to get affiliate stats: ${error.message}`,
        error.stack,
        'AffiliateService'
      );
      throw error;
    }
  }

  /**
   * Get user's affiliates
   */
  async getUserAffiliates(userId: string): Promise<Affiliate[]> {
    this.serviceLogger.logFind('user affiliates', { userId });

    try {
      const affiliates = await this.affiliateRepository.find({
        where: { userId },
        order: { createdAt: 'DESC' },
      });

      this.serviceLogger.logFindSuccess('user affiliates', affiliates.length, { userId });

      return affiliates;
    } catch (error) {
      this.logger.error(
        `Failed to get user affiliates: ${error.message}`,
        error.stack,
        'AffiliateService'
      );
      throw error;
    }
  }

  /**
   * Get referred users by affiliate
   */
  async getReferredUsers(affiliateId: string, page: number = 1, limit: number = 10): Promise<{
    users: User[];
    total: number;
    page: number;
    limit: number;
  }> {
    this.serviceLogger.logFind('referred users', { affiliateId, page, limit });

    try {
      const [users, total] = await this.userRepository.findAndCount({
        where: { referredByAffiliateId: affiliateId },
        select: ['id', 'username', 'fullName', 'email', 'createdAt'],
        skip: (page - 1) * limit,
        take: limit,
        order: { createdAt: 'DESC' },
      });

      this.serviceLogger.logFindSuccess('referred users', total, { affiliateId, page, limit });

      return {
        users,
        total,
        page,
        limit,
      };
    } catch (error) {
      this.logger.error(
        `Failed to get referred users: ${error.message}`,
        error.stack,
        'AffiliateService'
      );
      throw error;
    }
  }

  /**
   * Process referral when user registers
   */
  async processReferral(newUserId: string, affiliateCode: string): Promise<void> {
    this.serviceLogger.logCreate('referral processing', { newUserId, affiliateCode });

    try {
      const affiliate = await this.findByCode(affiliateCode);
      if (!affiliate || !affiliate.canBeUsed) {
        this.logger.warn(
          `Invalid or unusable affiliate code: ${affiliateCode}`,
          'AffiliateService'
        );
        return;
      }

      // Update user with affiliate reference
      await this.userRepository.update(newUserId, {
        referredByAffiliateId: affiliate.id,
      });

      this.serviceLogger.logCreateSuccess('referral processing', newUserId, {
        affiliateCode,
        affiliateId: affiliate.id,
      });
    } catch (error) {
      this.serviceLogger.logCreateError('referral processing', error, { newUserId, affiliateCode });
      throw error;
    }
  }

  /**
   * Delete affiliate
   */
  @AuditLog({
    action: AuditAction.DELETE,
    entityType: 'Affiliate',
    getEntityId: (args) => args[0], // affiliateId
    riskLevel: 'high',
    isSensitive: true,
  })
  async deleteAffiliate(affiliateId: string): Promise<void> {
    this.serviceLogger.logDelete('affiliate', affiliateId);

    try {
      const affiliate = await this.affiliateRepository.findOne({
        where: { id: affiliateId },
      });

      if (!affiliate) {
        this.serviceLogger.logNotFound('affiliate', affiliateId);
        throw new NotFoundException('Affiliate not found');
      }

      // Check if affiliate has been used
      const usageCount = await this.affiliateUsageRepository.count({
        where: { affiliateId },
      });

      if (usageCount > 0) {
        throw new BadRequestException('Cannot delete affiliate that has been used');
      }

      await this.affiliateRepository.remove(affiliate);

      this.serviceLogger.logDeleteSuccess('affiliate', affiliateId);
    } catch (error) {
      this.serviceLogger.logDeleteError('affiliate', affiliateId, error);
      throw error;
    }
  }

  private async generateUniqueCode(username: string): Promise<string> {
    const baseCode = `AFF-${username.toUpperCase()}-${new Date().getFullYear()}`;
    let counter = 1;
    let code = baseCode;

    while (await this.affiliateRepository.findOne({ where: { code } })) {
      code = `${baseCode}-${counter}`;
      counter++;
    }

    return code;
  }
}
