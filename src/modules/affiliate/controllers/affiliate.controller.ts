import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  HttpStatus,
  ParseUUIDPipe,
  UseGuards,
  ParseIntPipe,
  DefaultValuePipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { AffiliateService, CreateAffiliateDto, UpdateAffiliateDto } from '../services/affiliate.service';
import { AffiliateUsageService } from '../services/affiliate-usage.service';
import { JwtAuthGuard } from '@/common/guards/jwt-auth.guard';
import { RolesGuard } from '@/common/guards/roles.guard';
import { PermissionsGuard } from '@/common/guards/permissions.guard';
import { Roles } from '@/common/decorators/roles.decorator';
import { Permissions } from '@/common/decorators/permissions.decorator';
import { CurrentUser } from '@/common/decorators/current-user.decorator';
import { RoleType } from '@/common/constants/role.constant';
import { PermissionsType } from '@/common/constants/permissions.constant';

@ApiTags('Affiliate Management')
@Controller('affiliate')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class AffiliateController {
  constructor(
    private readonly affiliateService: AffiliateService,
    private readonly affiliateUsageService: AffiliateUsageService,
  ) {}

  @Post('create/:userId')
  @UseGuards(PermissionsGuard)
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER)
  @Permissions(PermissionsType.CREATE)
  @ApiOperation({ summary: 'Create affiliate code for a user' })
  @ApiParam({ name: 'userId', description: 'User ID', type: 'string' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Affiliate code created successfully',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'User not found',
  })
  @ApiResponse({
    status: HttpStatus.CONFLICT,
    description: 'Affiliate code already exists',
  })
  async createAffiliate(
    @Param('userId', ParseUUIDPipe) userId: string,
    @Body() createDto: CreateAffiliateDto,
  ) {
    return await this.affiliateService.createAffiliate(userId, createDto);
  }

  @Put(':affiliateId')
  @UseGuards(PermissionsGuard)
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER)
  @Permissions(PermissionsType.UPDATE)
  @ApiOperation({ summary: 'Update affiliate code' })
  @ApiParam({ name: 'affiliateId', description: 'Affiliate ID', type: 'string' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Affiliate code updated successfully',
  })
  async updateAffiliate(
    @Param('affiliateId', ParseUUIDPipe) affiliateId: string,
    @Body() updateDto: UpdateAffiliateDto,
  ) {
    return await this.affiliateService.updateAffiliate(affiliateId, updateDto);
  }

  @Put(':affiliateId/toggle')
  @UseGuards(PermissionsGuard)
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER)
  @Permissions(PermissionsType.UPDATE)
  @ApiOperation({ summary: 'Toggle affiliate code status' })
  @ApiParam({ name: 'affiliateId', description: 'Affiliate ID', type: 'string' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Affiliate status updated successfully',
  })
  async toggleAffiliateStatus(
    @Param('affiliateId', ParseUUIDPipe) affiliateId: string,
    @Body() body: { isActive: boolean },
  ) {
    return await this.affiliateService.toggleAffiliateStatus(affiliateId, body.isActive);
  }

  @Get('code/:code')
  @ApiOperation({ summary: 'Find affiliate by code' })
  @ApiParam({ name: 'code', description: 'Affiliate code', type: 'string' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Affiliate found',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Affiliate not found',
  })
  async findByCode(@Param('code') code: string) {
    return await this.affiliateService.findByCode(code);
  }

  @Get(':affiliateId/stats')
  @UseGuards(PermissionsGuard)
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER)
  @Permissions(PermissionsType.VIEW)
  @ApiOperation({ summary: 'Get affiliate statistics' })
  @ApiParam({ name: 'affiliateId', description: 'Affiliate ID', type: 'string' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Affiliate statistics retrieved successfully',
  })
  async getAffiliateStats(@Param('affiliateId', ParseUUIDPipe) affiliateId: string) {
    return await this.affiliateService.getAffiliateStats(affiliateId);
  }

  @Get('user/:userId')
  @UseGuards(PermissionsGuard)
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER)
  @Permissions(PermissionsType.VIEW)
  @ApiOperation({ summary: 'Get user affiliates' })
  @ApiParam({ name: 'userId', description: 'User ID', type: 'string' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'User affiliates retrieved successfully',
  })
  async getUserAffiliates(@Param('userId', ParseUUIDPipe) userId: string) {
    return await this.affiliateService.getUserAffiliates(userId);
  }

  @Get('my-affiliates')
  @ApiOperation({ summary: 'Get current user affiliates' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Current user affiliates retrieved successfully',
  })
  async getMyAffiliates(@CurrentUser() currentUser: any) {
    return await this.affiliateService.getUserAffiliates(currentUser.sub);
  }

  @Get(':affiliateId/referrals')
  @UseGuards(PermissionsGuard)
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER)
  @Permissions(PermissionsType.VIEW)
  @ApiOperation({ summary: 'Get users referred by affiliate' })
  @ApiParam({ name: 'affiliateId', description: 'Affiliate ID', type: 'string' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Items per page' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Referred users retrieved successfully',
  })
  async getReferredUsers(
    @Param('affiliateId', ParseUUIDPipe) affiliateId: string,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
  ) {
    return await this.affiliateService.getReferredUsers(affiliateId, page, limit);
  }

  @Get(':affiliateId/usage-history')
  @UseGuards(PermissionsGuard)
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER)
  @Permissions(PermissionsType.VIEW)
  @ApiOperation({ summary: 'Get affiliate usage history' })
  @ApiParam({ name: 'affiliateId', description: 'Affiliate ID', type: 'string' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Items per page' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Usage history retrieved successfully',
  })
  async getUsageHistory(
    @Param('affiliateId', ParseUUIDPipe) affiliateId: string,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query('limit', new DefaultValuePipe(20), ParseIntPipe) limit: number,
  ) {
    return await this.affiliateUsageService.getUsageHistory(affiliateId, page, limit);
  }

  @Get('analytics/top-performers')
  @UseGuards(PermissionsGuard)
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER)
  @Permissions(PermissionsType.VIEW)
  @ApiOperation({ summary: 'Get top performing affiliates' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Number of results' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Top performing affiliates retrieved successfully',
  })
  async getTopPerformingAffiliates(
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
  ) {
    return await this.affiliateUsageService.getTopPerformingAffiliates(limit);
  }

  @Delete(':affiliateId')
  @UseGuards(PermissionsGuard)
  @Roles(RoleType.SUPER_ADMIN)
  @Permissions(PermissionsType.DELETE)
  @ApiOperation({ summary: 'Delete affiliate code' })
  @ApiParam({ name: 'affiliateId', description: 'Affiliate ID', type: 'string' })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'Affiliate deleted successfully',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Cannot delete affiliate that has been used',
  })
  async deleteAffiliate(@Param('affiliateId', ParseUUIDPipe) affiliateId: string) {
    await this.affiliateService.deleteAffiliate(affiliateId);
    return { message: 'Affiliate deleted successfully' };
  }
}
