import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Exclude, Expose, Type } from 'class-transformer';

class UserSummaryDto {
  @Expose()
  id: string;

  @Expose()
  username: string;

  @Expose()
  fullName: string;

  @Expose()
  email: string;
}

class RoleSummaryDto {
  @Expose()
  id: string;

  @Expose()
  name: string;

  @Expose()
  displayName: string;

  @Expose()
  level: number;
}

class DepartmentSummaryDto {
  @Expose()
  id: string;

  @Expose()
  name: string;
}

class TeamSummaryDto {
  @Expose()
  id: string;

  @Expose()
  name: string;
}

export class UserRoleAssignmentResponseDto {
  @ApiProperty({
    description: 'Assignment ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @Expose()
  id: string;

  @ApiProperty({
    description: 'User ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @Expose()
  userId: string;

  @ApiProperty({
    description: 'Role ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @Expose()
  roleId: string;

  @ApiPropertyOptional({
    description: 'Department ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @Expose()
  departmentId?: string;

  @ApiPropertyOptional({
    description: 'Team ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @Expose()
  teamId?: string;

  @ApiProperty({
    description: 'Creation date',
    example: '2023-01-01T00:00:00.000Z',
  })
  @Expose()
  createdAt: Date;

  @ApiProperty({
    description: 'Last update date',
    example: '2023-01-01T00:00:00.000Z',
  })
  @Expose()
  updatedAt: Date;

  @ApiPropertyOptional({
    description: 'Created by',
    example: 'admin',
  })
  @Expose()
  createdBy?: string;

  @ApiPropertyOptional({
    description: 'Updated by',
    example: 'admin',
  })
  @Expose()
  updatedBy?: string;

  @ApiPropertyOptional({
    description: 'User information',
    type: UserSummaryDto,
  })
  @Expose()
  @Type(() => UserSummaryDto)
  user?: UserSummaryDto;

  @ApiPropertyOptional({
    description: 'Role information',
    type: RoleSummaryDto,
  })
  @Expose()
  @Type(() => RoleSummaryDto)
  role?: RoleSummaryDto;

  @ApiPropertyOptional({
    description: 'Department information',
    type: DepartmentSummaryDto,
  })
  @Expose()
  @Type(() => DepartmentSummaryDto)
  department?: DepartmentSummaryDto;

  @ApiPropertyOptional({
    description: 'Team information',
    type: TeamSummaryDto,
  })
  @Expose()
  @Type(() => TeamSummaryDto)
  team?: TeamSummaryDto;

  @Exclude()
  deletedAt?: Date;

  @Exclude()
  deletedBy?: string;

  @Exclude()
  metadata?: Record<string, any>;
}
