import { IsUUID, IsOptional } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreateUserRoleAssignmentDto {
  @ApiProperty({
    description: 'User ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  userId: string;

  @ApiProperty({
    description: 'Role ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  roleId: string;

  @ApiPropertyOptional({
    description: 'Department ID (optional context)',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsOptional()
  @IsUUID()
  departmentId?: string;

  @ApiPropertyOptional({
    description: 'Team ID (optional context)',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsOptional()
  @IsUUID()
  teamId?: string;
}
