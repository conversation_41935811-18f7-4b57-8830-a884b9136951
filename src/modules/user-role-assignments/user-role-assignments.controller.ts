import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  HttpStatus,
  ParseUUI<PERSON>ipe,
  UseGuards,
  Request,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  <PERSON>piQuery,
  A<PERSON><PERSON>earerA<PERSON>,
} from '@nestjs/swagger';
import { plainToClass } from 'class-transformer';
import { UserRoleAssignmentsService } from './user-role-assignments.service';
import { CreateUserRoleAssignmentDto } from './dto/create-user-role-assignment.dto';
import { UpdateUserRoleAssignmentDto } from './dto/update-user-role-assignment.dto';
import { BulkAssignRolesDto } from './dto/bulk-assign-roles.dto';
import { UserRoleAssignmentResponseDto } from './dto/user-role-assignment-response.dto';
import { PaginationDto, PaginatedResult } from '@/common/dto/pagination.dto';
import { JwtAuthGuard } from '@/common/guards/jwt-auth.guard';
import { RolesGuard } from '@/common/guards/roles.guard';
import { PermissionsGuard } from '@/common/guards/permissions.guard';
import { Roles } from '@/common/decorators/roles.decorator';
import { Permissions } from '@/common/decorators/permissions.decorator';
import { RoleType, PermissionsType } from '@/common/constants/permissions.type';

@ApiTags('User Role Assignments')
@Controller('user-role-assignments')
@UseGuards(JwtAuthGuard, RolesGuard, PermissionsGuard)

export class UserRoleAssignmentsController {
  constructor(private readonly userRoleAssignmentsService: UserRoleAssignmentsService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new user-role assignment' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Assignment created successfully',
    type: UserRoleAssignmentResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'User, role, department, or team not found',
  })
  @ApiResponse({
    status: HttpStatus.CONFLICT,
    description: 'Assignment already exists for this context',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Insufficient permissions',
  })
  @ApiBearerAuth()
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER)
  @Permissions(PermissionsType.CREATE)
  async create(@Body() createDto: CreateUserRoleAssignmentDto, @Request() req: any): Promise<UserRoleAssignmentResponseDto> {
    const assignment = await this.userRoleAssignmentsService.create(createDto, req.user.id, req.user.id);
    return plainToClass(UserRoleAssignmentResponseDto, assignment, { excludeExtraneousValues: true });
  }

  @Post('bulk-assign')
  @ApiOperation({ summary: 'Bulk assign roles to a user' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Roles assigned successfully',
    type: [UserRoleAssignmentResponseDto],
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'User, roles, department, or team not found',
  })
  @ApiResponse({
    status: HttpStatus.CONFLICT,
    description: 'All roles are already assigned',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Insufficient permissions',
  })
  @ApiBearerAuth()
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER)
  @Permissions(PermissionsType.CREATE)
  async bulkAssign(@Body() bulkAssignDto: BulkAssignRolesDto, @Request() req: any): Promise<UserRoleAssignmentResponseDto[]> {
    const assignments = await this.userRoleAssignmentsService.bulkAssign(bulkAssignDto, req.user.id, req.user.id);
    return assignments.map(assignment =>
      plainToClass(UserRoleAssignmentResponseDto, assignment, { excludeExtraneousValues: true })
    );
  }

  @Get()
  @ApiOperation({ summary: 'Get all user-role assignments with pagination and filters' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Assignments retrieved successfully',
  })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Items per page' })
  @ApiQuery({ name: 'search', required: false, type: String, description: 'Search term' })
  @ApiQuery({ name: 'sortBy', required: false, type: String, description: 'Sort field' })
  @ApiQuery({ name: 'sortOrder', required: false, enum: ['ASC', 'DESC'], description: 'Sort order' })
  @ApiQuery({ name: 'userId', required: false, type: String, description: 'Filter by user ID' })
  @ApiQuery({ name: 'roleId', required: false, type: String, description: 'Filter by role ID' })
  @ApiQuery({ name: 'departmentId', required: false, type: String, description: 'Filter by department ID' })
  @ApiQuery({ name: 'teamId', required: false, type: String, description: 'Filter by team ID' })
  async findAll(
    @Query() paginationDto: PaginationDto,
    @Query('userId') userId?: string,
    @Query('roleId') roleId?: string,
    @Query('departmentId') departmentId?: string,
    @Query('teamId') teamId?: string,
  ): Promise<PaginatedResult<UserRoleAssignmentResponseDto>> {
    const filters = { userId, roleId, departmentId, teamId };
    const result = await this.userRoleAssignmentsService.findAll(paginationDto, filters);
    
    return {
      ...result,
      data: result.data.map(assignment => 
        plainToClass(UserRoleAssignmentResponseDto, assignment, { excludeExtraneousValues: true })
      ),
    };
  }

  @Get('users/:userId/roles')
  @ApiOperation({ summary: 'Get roles assigned to a user' })
  @ApiParam({ name: 'userId', description: 'User ID', type: 'string' })
  @ApiQuery({ name: 'departmentId', required: false, type: String, description: 'Filter by department ID' })
  @ApiQuery({ name: 'teamId', required: false, type: String, description: 'Filter by team ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'User roles retrieved successfully',
  })
  @ApiBearerAuth()
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER)
  @Permissions(PermissionsType.VIEW)
  async getUserRoles(
    @Param('userId', ParseUUIDPipe) userId: string,
    @Query('departmentId') departmentId?: string,
    @Query('teamId') teamId?: string,
  ) {
    return await this.userRoleAssignmentsService.getUserRoles(userId, departmentId, teamId);
  }

  @Get('roles/:roleId/users')
  @ApiOperation({ summary: 'Get users assigned to a role' })
  @ApiParam({ name: 'roleId', description: 'Role ID', type: 'string' })
  @ApiQuery({ name: 'departmentId', required: false, type: String, description: 'Filter by department ID' })
  @ApiQuery({ name: 'teamId', required: false, type: String, description: 'Filter by team ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Role users retrieved successfully',
  })
  @ApiBearerAuth()
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER)
  @Permissions(PermissionsType.VIEW)
  async getUsersByRole(
    @Param('roleId', ParseUUIDPipe) roleId: string,
    @Query('departmentId') departmentId?: string,
    @Query('teamId') teamId?: string,
  ) {
    return await this.userRoleAssignmentsService.getUsersByRole(roleId, departmentId, teamId);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get user-role assignment by ID' })
  @ApiParam({ name: 'id', description: 'Assignment ID', type: 'string' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Assignment retrieved successfully',
    type: UserRoleAssignmentResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Assignment not found',
  })
  @ApiBearerAuth()
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER)
  @Permissions(PermissionsType.VIEW)
  async findOne(@Param('id', ParseUUIDPipe) id: string): Promise<UserRoleAssignmentResponseDto> {
    const assignment = await this.userRoleAssignmentsService.findOne(id);
    return plainToClass(UserRoleAssignmentResponseDto, assignment, { excludeExtraneousValues: true });
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update user-role assignment by ID' })
  @ApiParam({ name: 'id', description: 'Assignment ID', type: 'string' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Assignment updated successfully',
    type: UserRoleAssignmentResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Assignment not found',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Department or team not found',
  })
  @ApiBearerAuth()
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER)
  @Permissions(PermissionsType.UPDATE)
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateDto: UpdateUserRoleAssignmentDto,
  ): Promise<UserRoleAssignmentResponseDto> {
    const assignment = await this.userRoleAssignmentsService.update(id, updateDto, 'system');
    return plainToClass(UserRoleAssignmentResponseDto, assignment, { excludeExtraneousValues: true });
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete user-role assignment by ID' })
  @ApiParam({ name: 'id', description: 'Assignment ID', type: 'string' })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'Assignment deleted successfully',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Assignment not found',
  })
  @ApiBearerAuth()
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER)
  @Permissions(PermissionsType.DELETE)
  async remove(@Param('id', ParseUUIDPipe) id: string): Promise<void> {
    await this.userRoleAssignmentsService.remove(id, 'system');
  }

  @Delete('users/:userId/roles')
  @ApiOperation({ summary: 'Remove roles from a user' })
  @ApiParam({ name: 'userId', description: 'User ID', type: 'string' })
  @ApiQuery({ name: 'departmentId', required: false, type: String, description: 'Department context' })
  @ApiQuery({ name: 'teamId', required: false, type: String, description: 'Team context' })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'Roles removed successfully',
  })
  @ApiBearerAuth()
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER)
  @Permissions(PermissionsType.DELETE)
  async removeUserRoles(
    @Param('userId', ParseUUIDPipe) userId: string,
    @Body() body: { roleIds: string[] },
    @Query('departmentId') departmentId?: string,
    @Query('teamId') teamId?: string,
  ): Promise<void> {
    await this.userRoleAssignmentsService.removeByUserAndRoles(
      userId,
      body.roleIds,
      departmentId,
      teamId
    );
  }
}
