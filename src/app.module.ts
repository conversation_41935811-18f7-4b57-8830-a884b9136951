import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { LoggerModule } from './common/logger/logger.module';
import configuration from './config/configuration';

// Import all modules
import { UsersModule } from './modules/users/users.module';
import { DepartmentsModule } from './modules/departments/departments.module';
import { TeamsModule } from './modules/teams/teams.module';
import { PermissionGroupsModule } from './modules/permission-groups/permission-groups.module';
import { PermissionsModule } from './modules/permissions/permissions.module';
import { RolesModule } from './modules/roles/roles.module';
import { RolePermissionAssignmentsModule } from './modules/role-permission-assignments/role-permission-assignments.module';
import { UserRoleAssignmentsModule } from './modules/user-role-assignments/user-role-assignments.module';
import { AuthModule } from './modules/auth/auth.module';
import { UploadModule } from './modules/upload/upload.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      load: [configuration],
      envFilePath: ['.env.local', '.env'],
    }),
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        type: 'mysql',
        host: configService.get('database.host'),
        port: configService.get('database.port'),
        username: configService.get('database.username'),
        password: configService.get('database.password'),
        database: configService.get('database.database'),
        entities: [__dirname + '/**/*.entity{.ts,.js}'],
        synchronize: configService.get('database.synchronize'),
        logging: configService.get('database.logging'),
        maxQueryExecutionTime: 1000,
        extra: {},
      }),
      inject: [ConfigService],
    }),
    LoggerModule,
    // RBAC Modules
    UsersModule,
    DepartmentsModule,
    TeamsModule,
    PermissionsModule,
    RolesModule,
    PermissionGroupsModule,
    RolePermissionAssignmentsModule,
    UserRoleAssignmentsModule,
    // Auth Module
    AuthModule,
    // Upload Module
    UploadModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
