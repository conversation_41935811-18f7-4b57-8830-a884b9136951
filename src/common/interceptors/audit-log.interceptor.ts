import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap, catchError } from 'rxjs/operators';
import { Request, Response } from 'express';
import { AuditLogService } from '@/common/services/audit-log.service';
import { AuditAction, AuditStatus } from '@/entities/audit-log.entity';
import { CustomLoggerService } from '@/common/logger/logger.service';

interface AuditableRequest extends Request {
  user?: {
    sub: string;
    username: string;
    [key: string]: any;
  };
  auditContext?: {
    entityType?: string;
    entityId?: string;
    action?: AuditAction;
    skipAudit?: boolean;
    riskLevel?: string;
    isSensitive?: boolean;
    businessContext?: string;
  };
}

@Injectable()
export class AuditLogInterceptor implements NestInterceptor {
  constructor(
    private readonly auditLogService: AuditLogService,
    private readonly logger: CustomLoggerService,
  ) {}

  intercept(context: ExecutionContext, next: <PERSON><PERSON><PERSON><PERSON>): Observable<any> {
    const request = context.switchToHttp().getRequest<AuditableRequest>();
    const response = context.switchToHttp().getResponse<Response>();
    
    // Skip audit for certain endpoints
    if (this.shouldSkipAudit(request)) {
      return next.handle();
    }

    const startTime = Date.now();
    const { method, url, headers, body, query, params } = request;
    const userAgent = headers['user-agent'] || '';
    const ipAddress = this.getClientIp(request);
    const userId = request.user?.sub;
    const userName = request.user?.username;

    // Determine audit action based on HTTP method and URL
    const auditAction = this.determineAuditAction(method, url);
    const entityType = this.extractEntityType(url);
    const entityId = this.extractEntityId(params, body);

    // Use custom audit context if provided
    const auditContext = request.auditContext || {};

    return next.handle().pipe(
      tap({
        next: (data) => {
          const duration = Date.now() - startTime;
          const { statusCode } = response;

          // Log successful operation
          this.auditLogService.createAuditLog({
            action: auditContext.action || auditAction,
            entityType: auditContext.entityType || entityType,
            entityId: auditContext.entityId || entityId,
            userId,
            userName,
            status: AuditStatus.SUCCESS,
            ipAddress,
            userAgent,
            endpoint: url,
            httpMethod: method,
            duration,
            queryParams: query,
            requestBody: this.sanitizeRequestBody(body),
            responseStatus: statusCode,
            riskLevel: auditContext.riskLevel || this.calculateRiskLevel(method, url),
            isSensitive: auditContext.isSensitive || this.isSensitiveEndpoint(url),
            businessContext: auditContext.businessContext,
            metadata: {
              responseSize: JSON.stringify(data).length,
              userAgent,
            },
          }).catch(error => {
            this.logger.error(
              `Failed to create audit log: ${error.message}`,
              error.stack,
              'AuditLogInterceptor'
            );
          });
        },
        error: (error) => {
          const duration = Date.now() - startTime;
          const statusCode = error.status || 500;

          // Log failed operation
          this.auditLogService.createAuditLog({
            action: auditContext.action || auditAction,
            entityType: auditContext.entityType || entityType,
            entityId: auditContext.entityId || entityId,
            userId,
            userName,
            status: AuditStatus.FAILED,
            ipAddress,
            userAgent,
            endpoint: url,
            httpMethod: method,
            duration,
            queryParams: query,
            requestBody: this.sanitizeRequestBody(body),
            responseStatus: statusCode,
            errorMessage: error.message,
            stackTrace: error.stack,
            riskLevel: auditContext.riskLevel || this.calculateRiskLevel(method, url),
            isSensitive: auditContext.isSensitive || this.isSensitiveEndpoint(url),
            businessContext: auditContext.businessContext,
            metadata: {
              errorType: error.constructor.name,
              userAgent,
            },
          }).catch(auditError => {
            this.logger.error(
              `Failed to create audit log for error: ${auditError.message}`,
              auditError.stack,
              'AuditLogInterceptor'
            );
          });
        },
      }),
      catchError((error) => {
        throw error;
      }),
    );
  }

  private shouldSkipAudit(request: AuditableRequest): boolean {
    const { method, url } = request;
    
    // Skip audit for certain endpoints
    const skipPatterns = [
      '/health',
      '/metrics',
      '/favicon.ico',
      '/uploads/',
      '/api-docs',
      '/swagger',
    ];

    // Skip GET requests to non-sensitive endpoints
    if (method === 'GET' && !this.isSensitiveEndpoint(url)) {
      return true;
    }

    // Skip if explicitly marked to skip
    if (request.auditContext?.skipAudit) {
      return true;
    }

    return skipPatterns.some(pattern => url.includes(pattern));
  }

  private determineAuditAction(method: string, url: string): AuditAction {
    const urlLower = url.toLowerCase();

    if (urlLower.includes('/login')) return AuditAction.LOGIN;
    if (urlLower.includes('/logout')) return AuditAction.LOGOUT;
    if (urlLower.includes('/password')) return AuditAction.PASSWORD_CHANGE;
    if (urlLower.includes('/import')) return AuditAction.IMPORT;
    if (urlLower.includes('/export')) return AuditAction.EXPORT;
    if (urlLower.includes('/bulk')) {
      switch (method.toUpperCase()) {
        case 'POST': return AuditAction.BULK_CREATE;
        case 'PUT':
        case 'PATCH': return AuditAction.BULK_UPDATE;
        case 'DELETE': return AuditAction.BULK_DELETE;
      }
    }

    switch (method.toUpperCase()) {
      case 'POST': return AuditAction.CREATE;
      case 'PUT':
      case 'PATCH': return AuditAction.UPDATE;
      case 'DELETE': return AuditAction.DELETE;
      default: return AuditAction.CREATE; // Default fallback
    }
  }

  private extractEntityType(url: string): string {
    // Extract entity type from URL path
    const pathSegments = url.split('/').filter(segment => segment && !segment.match(/^[0-9a-f-]{36}$/i));
    
    // Remove common prefixes
    const filteredSegments = pathSegments.filter(segment => 
      !['api', 'v1', 'v2', 'admin'].includes(segment.toLowerCase())
    );

    if (filteredSegments.length > 0) {
      // Convert plural to singular and capitalize
      const entityType = filteredSegments[0];
      return entityType.charAt(0).toUpperCase() + entityType.slice(1).replace(/s$/, '');
    }

    return 'Unknown';
  }

  private extractEntityId(params: any, body: any): string | undefined {
    // Try to extract ID from URL parameters
    if (params?.id) return params.id;
    if (params?.userId) return params.userId;
    if (params?.affiliateId) return params.affiliateId;
    
    // Try to extract ID from request body
    if (body?.id) return body.id;
    
    return undefined;
  }

  private calculateRiskLevel(method: string, url: string): string {
    const urlLower = url.toLowerCase();
    
    // High risk operations
    if (method === 'DELETE' || urlLower.includes('/delete') || urlLower.includes('/bulk')) {
      return 'high';
    }
    
    // Medium risk operations
    if (urlLower.includes('/password') || urlLower.includes('/permission') || urlLower.includes('/role')) {
      return 'medium';
    }
    
    // Sensitive endpoints
    if (this.isSensitiveEndpoint(url)) {
      return 'medium';
    }
    
    return 'low';
  }

  private isSensitiveEndpoint(url: string): boolean {
    const sensitivePatterns = [
      '/users',
      '/auth',
      '/roles',
      '/permissions',
      '/affiliate',
      '/admin',
      '/password',
      '/login',
      '/logout',
    ];
    
    return sensitivePatterns.some(pattern => url.toLowerCase().includes(pattern));
  }

  private sanitizeRequestBody(body: any): any {
    if (!body) return body;

    const sensitiveFields = [
      'password',
      'token',
      'secret',
      'key',
      'authorization',
      'creditCard',
      'ssn',
      'socialSecurityNumber',
    ];

    const sanitized = { ...body };

    for (const field of sensitiveFields) {
      if (sanitized[field]) {
        sanitized[field] = '***REDACTED***';
      }
    }

    return sanitized;
  }

  private getClientIp(request: Request): string {
    return (
      request.headers['x-forwarded-for'] as string ||
      request.headers['x-real-ip'] as string ||
      request.connection?.remoteAddress ||
      request.socket?.remoteAddress ||
      (request.connection as any)?.socket?.remoteAddress ||
      request.ip ||
      'unknown'
    );
  }
}
