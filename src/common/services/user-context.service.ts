import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from '../../entities/user.entity';
import { Role } from '../../entities/role.entity';
import { UserRoleAssignment } from '../../entities/user-role-assignment.entity';
import { RoleType } from '../constants/permissions.type';

export interface UserContext {
  id: string;
  username: string;
  email: string;
  roles: string[];
  permissions: string[];
  userRoles: UserRoleAssignment[];
  highestRole: Role | undefined;
  departments: string[];
  teams: string[];
}

@Injectable()
export class UserContextService {
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(Role)
    private readonly roleRepository: Repository<Role>,
  ) {}

  async getUserContext(userId: string): Promise<UserContext> {
    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: [
        'userRoles',
        'userRoles.role',
        'userRoles.role.rolePermissions',
        'userRoles.role.rolePermissions.permission',
        'userRoles.department',
        'userRoles.team',
      ],
    });

    if (!user) {
      throw new Error('User not found');
    }

    // Get unique permissions from all user roles
    const permissions = new Set<string>();
    user.userRoles?.forEach((userRole) => {
      userRole.role.rolePermissions?.forEach((rolePermission) => {
        permissions.add(rolePermission.permission.action);
      });
    });

    // Get highest role (lowest level number)
    const highestRole = user.userRoles?.reduce((highest, userRole) => {
      if (!highest || userRole.role.level < highest.level) {
        return userRole.role;
      }
      return highest;
    }, undefined as Role | undefined);

    // Get unique departments and teams
    const departments = new Set<string>();
    const teams = new Set<string>();
    user.userRoles?.forEach((userRole) => {
      if (userRole.departmentId) {
        departments.add(userRole.departmentId);
      }
      if (userRole.teamId) {
        teams.add(userRole.teamId);
      }
    });

    return {
      id: user.id,
      username: user.username,
      email: user.email,
      roles: user.userRoles?.map((ura) => ura.role.name) || [],
      permissions: Array.from(permissions),
      userRoles: user.userRoles || [],
      highestRole,
      departments: Array.from(departments),
      teams: Array.from(teams),
    };
  }

  canAssignRole(currentUserRole: Role, targetRole: Role): boolean {
    // Super admin can assign any role
    if (currentUserRole.name === RoleType.SUPER_ADMIN) {
      return true;
    }

    // Users can only assign roles with higher level (lower privilege)
    return targetRole.level > currentUserRole.level;
  }

  canManageDepartment(userContext: UserContext, departmentId: string): boolean {
    // Super admin can manage any department
    if (userContext.highestRole?.name === RoleType.SUPER_ADMIN) {
      return true;
    }

    // Manager and Team Leader can only manage their own departments
    return userContext.departments.includes(departmentId);
  }

  canManageTeam(userContext: UserContext, teamId: string, departmentId?: string): boolean {
    // Super admin can manage any team
    if (userContext.highestRole?.name === RoleType.SUPER_ADMIN) {
      return true;
    }

    // Check if user can manage the department first
    if (departmentId && !this.canManageDepartment(userContext, departmentId)) {
      return false;
    }

    // Team Leader and Manager can manage teams in their departments
    return userContext.teams.includes(teamId) ||
           (!!departmentId && userContext.departments.includes(departmentId));
  }

  getAssignableRoles(currentUserRole: Role): Promise<Role[]> {
    if (currentUserRole.name === RoleType.SUPER_ADMIN) {
      // Super admin can assign any role
      return this.roleRepository.find({
        order: { level: 'ASC' },
      });
    }

    // Other users can only assign roles with higher level (lower privilege)
    return this.roleRepository.find({
      where: {
        level: currentUserRole.level + 1, // Only roles with higher level
      },
      order: { level: 'ASC' },
    });
  }

  getUserDepartmentContext(userContext: UserContext): string[] {
    // Super admin has access to all departments (return empty array to indicate no restriction)
    if (userContext.highestRole?.name === RoleType.SUPER_ADMIN) {
      return [];
    }

    // Return user's departments
    return userContext.departments;
  }

  getUserTeamContext(userContext: UserContext): string[] {
    // Super admin has access to all teams
    if (userContext.highestRole?.name === RoleType.SUPER_ADMIN) {
      return [];
    }

    // Return user's teams
    return userContext.teams;
  }
}
