import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AuditLog, AuditAction, AuditStatus } from '@/entities/audit-log.entity';
import { CustomLoggerService } from '@/common/logger/logger.service';

export interface CreateAuditLogDto {
  action: AuditAction;
  entityType: string;
  entityId?: string;
  tableName?: string;
  userId?: string;
  userName?: string;
  status?: AuditStatus;
  ipAddress?: string;
  userAgent?: string;
  endpoint?: string;
  httpMethod?: string;
  oldValues?: Record<string, any>;
  newValues?: Record<string, any>;
  description?: string;
  errorMessage?: string;
  stackTrace?: string;
  duration?: number;
  metadata?: Record<string, any>;
  sessionId?: string;
  requestId?: string;
  module?: string;
  method?: string;
  recordsAffected?: number;
  batchId?: string;
  queryParams?: Record<string, any>;
  requestBody?: Record<string, any>;
  responseStatus?: number;
  riskLevel?: string;
  isSensitive?: boolean;
  businessContext?: string;
}

export interface AuditLogQueryDto {
  entityType?: string;
  entityId?: string;
  action?: AuditAction;
  userId?: string;
  status?: AuditStatus;
  startDate?: Date;
  endDate?: Date;
  ipAddress?: string;
  riskLevel?: string;
  isSensitive?: boolean;
  page?: number;
  limit?: number;
}

@Injectable()
export class AuditLogService {
  constructor(
    @InjectRepository(AuditLog)
    private readonly auditLogRepository: Repository<AuditLog>,
    private readonly logger: CustomLoggerService,
  ) {}

  /**
   * Create audit log entry
   */
  async createAuditLog(auditData: CreateAuditLogDto): Promise<AuditLog> {
    try {
      // Calculate changes if both old and new values are provided
      let changes: Record<string, { from: any; to: any }> | undefined;
      if (auditData.oldValues && auditData.newValues) {
        changes = this.calculateChanges(auditData.oldValues, auditData.newValues);
      }

      const auditLog = this.auditLogRepository.create({
        ...auditData,
        changes,
        status: auditData.status || AuditStatus.SUCCESS,
        riskLevel: auditData.riskLevel || 'low',
        isSensitive: auditData.isSensitive || false,
      });

      const savedAuditLog = await this.auditLogRepository.save(auditLog);

      // Log high-risk or sensitive operations
      if (auditData.riskLevel === 'high' || auditData.riskLevel === 'critical' || auditData.isSensitive) {
        this.logger.logSecurity(
          `${auditData.action} on ${auditData.entityType}`,
          auditData.riskLevel as any || 'medium',
          {
            entityId: auditData.entityId,
            userId: auditData.userId,
            action: auditData.action,
            entityType: auditData.entityType,
          },
          auditData.ipAddress
        );
      }

      return savedAuditLog;
    } catch (error) {
      this.logger.error(
        `Failed to create audit log: ${error.message}`,
        error.stack,
        'AuditLogService'
      );
      throw error;
    }
  }

  /**
   * Log CRUD operation
   */
  async logCrudOperation(
    action: AuditAction,
    entityType: string,
    entityId: string,
    userId?: string,
    oldValues?: Record<string, any>,
    newValues?: Record<string, any>,
    additionalData?: Partial<CreateAuditLogDto>
  ): Promise<AuditLog> {
    return this.createAuditLog({
      action,
      entityType,
      entityId,
      userId,
      oldValues,
      newValues,
      tableName: this.getTableName(entityType),
      riskLevel: this.calculateRiskLevel(action, entityType),
      isSensitive: this.isSensitiveEntity(entityType),
      ...additionalData,
    });
  }

  /**
   * Log bulk operation
   */
  async logBulkOperation(
    action: AuditAction,
    entityType: string,
    recordsAffected: number,
    userId?: string,
    batchId?: string,
    additionalData?: Partial<CreateAuditLogDto>
  ): Promise<AuditLog> {
    return this.createAuditLog({
      action,
      entityType,
      userId,
      recordsAffected,
      batchId,
      tableName: this.getTableName(entityType),
      riskLevel: recordsAffected > 100 ? 'high' : recordsAffected > 10 ? 'medium' : 'low',
      isSensitive: this.isSensitiveEntity(entityType),
      ...additionalData,
    });
  }

  /**
   * Log authentication event
   */
  async logAuthEvent(
    action: AuditAction,
    userId?: string,
    userName?: string,
    success: boolean = true,
    ipAddress?: string,
    userAgent?: string,
    additionalData?: Partial<CreateAuditLogDto>
  ): Promise<AuditLog> {
    return this.createAuditLog({
      action,
      entityType: 'Authentication',
      userId,
      userName,
      status: success ? AuditStatus.SUCCESS : AuditStatus.FAILED,
      ipAddress,
      userAgent,
      riskLevel: success ? 'low' : 'medium',
      isSensitive: true,
      ...additionalData,
    });
  }

  /**
   * Log import/export operation
   */
  async logImportExportOperation(
    action: AuditAction.IMPORT | AuditAction.EXPORT,
    entityType: string,
    recordsAffected: number,
    userId?: string,
    fileName?: string,
    additionalData?: Partial<CreateAuditLogDto>
  ): Promise<AuditLog> {
    return this.createAuditLog({
      action,
      entityType,
      userId,
      recordsAffected,
      metadata: { fileName },
      riskLevel: recordsAffected > 1000 ? 'high' : recordsAffected > 100 ? 'medium' : 'low',
      isSensitive: this.isSensitiveEntity(entityType),
      businessContext: `${action} operation for ${entityType}`,
      ...additionalData,
    });
  }

  /**
   * Query audit logs
   */
  async queryAuditLogs(queryDto: AuditLogQueryDto): Promise<{
    logs: AuditLog[];
    total: number;
    page: number;
    limit: number;
  }> {
    try {
      const queryBuilder = this.auditLogRepository
        .createQueryBuilder('audit')
        .leftJoinAndSelect('audit.user', 'user')
        .leftJoinAndSelect('audit.approver', 'approver');

      // Apply filters
      if (queryDto.entityType) {
        queryBuilder.andWhere('audit.entityType = :entityType', { entityType: queryDto.entityType });
      }

      if (queryDto.entityId) {
        queryBuilder.andWhere('audit.entityId = :entityId', { entityId: queryDto.entityId });
      }

      if (queryDto.action) {
        queryBuilder.andWhere('audit.action = :action', { action: queryDto.action });
      }

      if (queryDto.userId) {
        queryBuilder.andWhere('audit.userId = :userId', { userId: queryDto.userId });
      }

      if (queryDto.status) {
        queryBuilder.andWhere('audit.status = :status', { status: queryDto.status });
      }

      if (queryDto.ipAddress) {
        queryBuilder.andWhere('audit.ipAddress = :ipAddress', { ipAddress: queryDto.ipAddress });
      }

      if (queryDto.riskLevel) {
        queryBuilder.andWhere('audit.riskLevel = :riskLevel', { riskLevel: queryDto.riskLevel });
      }

      if (queryDto.isSensitive !== undefined) {
        queryBuilder.andWhere('audit.isSensitive = :isSensitive', { isSensitive: queryDto.isSensitive });
      }

      if (queryDto.startDate && queryDto.endDate) {
        queryBuilder.andWhere('audit.createdAt BETWEEN :startDate AND :endDate', {
          startDate: queryDto.startDate,
          endDate: queryDto.endDate,
        });
      }

      // Pagination
      const page = queryDto.page || 1;
      const limit = queryDto.limit || 20;
      const skip = (page - 1) * limit;

      queryBuilder
        .orderBy('audit.createdAt', 'DESC')
        .skip(skip)
        .take(limit);

      const [logs, total] = await queryBuilder.getManyAndCount();

      return {
        logs,
        total,
        page,
        limit,
      };
    } catch (error) {
      this.logger.error(
        `Failed to query audit logs: ${error.message}`,
        error.stack,
        'AuditLogService'
      );
      throw error;
    }
  }

  /**
   * Get audit statistics
   */
  async getAuditStatistics(startDate?: Date, endDate?: Date): Promise<any> {
    try {
      const queryBuilder = this.auditLogRepository.createQueryBuilder('audit');

      if (startDate && endDate) {
        queryBuilder.where('audit.createdAt BETWEEN :startDate AND :endDate', {
          startDate,
          endDate,
        });
      }

      const [
        totalLogs,
        successfulOperations,
        failedOperations,
        sensitiveOperations,
        highRiskOperations,
      ] = await Promise.all([
        queryBuilder.getCount(),
        queryBuilder.clone().andWhere('audit.status = :status', { status: AuditStatus.SUCCESS }).getCount(),
        queryBuilder.clone().andWhere('audit.status = :status', { status: AuditStatus.FAILED }).getCount(),
        queryBuilder.clone().andWhere('audit.isSensitive = :isSensitive', { isSensitive: true }).getCount(),
        queryBuilder.clone().andWhere('audit.riskLevel IN (:...riskLevels)', { riskLevels: ['high', 'critical'] }).getCount(),
      ]);

      // Get action distribution
      const actionStats = await queryBuilder
        .clone()
        .select('audit.action', 'action')
        .addSelect('COUNT(*)', 'count')
        .groupBy('audit.action')
        .getRawMany();

      // Get entity type distribution
      const entityStats = await queryBuilder
        .clone()
        .select('audit.entityType', 'entityType')
        .addSelect('COUNT(*)', 'count')
        .groupBy('audit.entityType')
        .getRawMany();

      return {
        summary: {
          totalLogs,
          successfulOperations,
          failedOperations,
          sensitiveOperations,
          highRiskOperations,
          successRate: totalLogs > 0 ? (successfulOperations / totalLogs) * 100 : 0,
        },
        actionDistribution: actionStats,
        entityDistribution: entityStats,
      };
    } catch (error) {
      this.logger.error(
        `Failed to get audit statistics: ${error.message}`,
        error.stack,
        'AuditLogService'
      );
      throw error;
    }
  }

  private calculateChanges(oldValues: Record<string, any>, newValues: Record<string, any>): Record<string, { from: any; to: any }> {
    const changes: Record<string, { from: any; to: any }> = {};

    // Check for modified and new fields
    for (const [key, newValue] of Object.entries(newValues)) {
      const oldValue = oldValues[key];
      if (JSON.stringify(oldValue) !== JSON.stringify(newValue)) {
        changes[key] = { from: oldValue, to: newValue };
      }
    }

    // Check for deleted fields
    for (const [key, oldValue] of Object.entries(oldValues)) {
      if (!(key in newValues)) {
        changes[key] = { from: oldValue, to: undefined };
      }
    }

    return changes;
  }

  private getTableName(entityType: string): string {
    // Convert entity type to table name (simple snake_case conversion)
    return entityType.toLowerCase().replace(/([A-Z])/g, '_$1').replace(/^_/, '');
  }

  private calculateRiskLevel(action: AuditAction, entityType: string): string {
    // Define risk levels based on action and entity type
    const highRiskActions = [AuditAction.DELETE, AuditAction.BULK_DELETE, AuditAction.PERMISSION_CHANGE];
    const sensitiveEntities = ['User', 'Role', 'Permission', 'Affiliate'];

    if (highRiskActions.includes(action)) {
      return 'high';
    }

    if (sensitiveEntities.includes(entityType)) {
      return 'medium';
    }

    return 'low';
  }

  private isSensitiveEntity(entityType: string): boolean {
    const sensitiveEntities = ['User', 'Role', 'Permission', 'Affiliate', 'AuditLog'];
    return sensitiveEntities.includes(entityType);
  }
}

/**
 * Decorator to automatically audit method calls
 */
export function AuditLog(options: {
  action: AuditAction;
  entityType: string;
  getEntityId?: (args: any[]) => string;
  riskLevel?: string;
  isSensitive?: boolean;
}) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      const auditLogService: AuditLogService = this.auditLogService;
      const startTime = Date.now();

      let entityId: string | undefined;
      if (options.getEntityId) {
        entityId = options.getEntityId(args);
      }

      try {
        const result = await method.apply(this, args);
        const duration = Date.now() - startTime;

        // Log successful operation
        if (auditLogService) {
          await auditLogService.createAuditLog({
            action: options.action,
            entityType: options.entityType,
            entityId,
            duration,
            status: AuditStatus.SUCCESS,
            module: target.constructor.name,
            method: propertyName,
            riskLevel: options.riskLevel,
            isSensitive: options.isSensitive,
          });
        }

        return result;
      } catch (error) {
        const duration = Date.now() - startTime;

        // Log failed operation
        if (auditLogService) {
          await auditLogService.createAuditLog({
            action: options.action,
            entityType: options.entityType,
            entityId,
            duration,
            status: AuditStatus.FAILED,
            errorMessage: error.message,
            stackTrace: error.stack,
            module: target.constructor.name,
            method: propertyName,
            riskLevel: options.riskLevel,
            isSensitive: options.isSensitive,
          });
        }

        throw error;
      }
    };

    return descriptor;
  };
}
