import { <PERSON><PERSON><PERSON>, <PERSON>umn, <PERSON>To<PERSON>ne, OneToMany, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'typeorm';
import { BaseEntity } from './base.entity';
import { Department } from './department.entity';
import { UserRoleAssignment } from './user-role-assignment.entity';
import { RolePermissionAssignment } from './role-permission-assignment.entity';

/**
 * Team Entity
 * Represents teams within departments
 */
@Entity('teams')
export class Team extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 255,
    comment: 'Name of the team',
  })
  name: string;

  @Column({
    type: 'uuid',
    comment: 'Department ID that this team belongs to',
  })
  departmentId: string;

  // Relationships
  @ManyToOne(() => Department, (department) => department.teams)
  @JoinColumn({ name: 'departmentId' })
  department: Department;

  @OneToMany(() => UserRoleAssignment, (userRole) => userRole.team)
  userRoles: UserRoleAssignment[];

  @OneToMany(() => RolePermissionAssignment, (rolePermission) => rolePermission.team)
  rolePermissions: RolePermissionAssignment[];
}
