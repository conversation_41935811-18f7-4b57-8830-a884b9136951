import { <PERSON>ti<PERSON>, Column, OneToMany } from 'typeorm';
import { BaseEntity } from './base.entity';
import { PermissionGroupDetail } from './permission-group-detail.entity';

/**
 * PermissionGroup Entity
 * Represents groups of permissions
 */
@Entity('permission_groups')
export class PermissionGroup extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 255,
    comment: 'Name of the permission group',
  })
  name: string;

  // Relationships
  @OneToMany(() => PermissionGroupDetail, (groupDetail) => groupDetail.permissionGroup)
  permissionDetails: PermissionGroupDetail[];
}
