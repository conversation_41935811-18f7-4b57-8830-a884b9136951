import { <PERSON>ti<PERSON>, Column, <PERSON>To<PERSON>ne, Join<PERSON><PERSON>umn } from 'typeorm';
import { BaseEntity } from './base.entity';
import { User } from './user.entity';
import { Role } from './role.entity';
import { Department } from './department.entity';
import { Team } from './team.entity';

/**
 * UserRoleAssignment Entity
 * Junction table between User and Role with Department/Team context
 */
@Entity('user_role_assignments')
export class UserRoleAssignment extends BaseEntity {
  @Column({
    type: 'uuid',
    comment: 'User ID',
  })
  userId: string;

  @Column({
    type: 'uuid',
    comment: 'Role ID',
  })
  roleId: string;

  @Column({
    type: 'uuid',
    nullable: true,
    comment: 'Department ID (optional context)',
  })
  departmentId?: string;

  @Column({
    type: 'uuid',
    nullable: true,
    comment: 'Team ID (optional context)',
  })
  teamId?: string;

  // Relationships
  @ManyToOne(() => User, (user) => user.userRoles)
  @JoinColumn({ name: 'userId' })
  user: User;

  @ManyToOne(() => Role, (role) => role.userRoles)
  @JoinColumn({ name: 'roleId' })
  role: Role;

  @ManyToOne(() => Department, (department) => department.userRoles)
  @JoinColumn({ name: 'departmentId' })
  department?: Department;

  @ManyToOne(() => Team, (team) => team.userRoles)
  @JoinColumn({ name: 'teamId' })
  team?: Team;
}
