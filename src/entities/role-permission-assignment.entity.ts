import { <PERSON>tity, Column, <PERSON>To<PERSON>ne, Join<PERSON><PERSON>umn } from 'typeorm';
import { BaseEntity } from './base.entity';
import { Role } from './role.entity';
import { Permission } from './permission.entity';
import { Department } from './department.entity';
import { Team } from './team.entity';

/**
 * RolePermissionAssignment Entity
 * Junction table between Role and Permission with Department/Team context
 */
@Entity('role_permission_assignments')
export class RolePermissionAssignment extends BaseEntity {
  @Column({
    type: 'uuid',
    comment: 'Role ID',
  })
  roleId: string;

  @Column({
    type: 'uuid',
    comment: 'Permission ID',
  })
  permissionId: string;

  @Column({
    type: 'uuid',
    nullable: true,
    comment: 'Department ID (optional context)',
  })
  departmentId?: string;

  @Column({
    type: 'uuid',
    nullable: true,
    comment: 'Team ID (optional context)',
  })
  teamId?: string;

  // Relationships
  @ManyToOne(() => Role, (role) => role.rolePermissions)
  @JoinColumn({ name: 'roleId' })
  role: Role;

  @ManyToOne(() => Permission, (permission) => permission.rolePermissions)
  @JoinColumn({ name: 'permissionId' })
  permission: Permission;

  @ManyToOne(() => Department, (department) => department.rolePermissions)
  @JoinColumn({ name: 'departmentId' })
  department?: Department;

  @ManyToOne(() => Team, (team) => team.rolePermissions)
  @JoinColumn({ name: 'teamId' })
  team?: Team;
}
