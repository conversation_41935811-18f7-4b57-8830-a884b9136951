import { <PERSON>ti<PERSON>, Column, OneToMany } from 'typeorm';
import { BaseEntity } from './base.entity';
import { Team } from './team.entity';
import { UserRoleAssignment } from './user-role-assignment.entity';
import { RolePermissionAssignment } from './role-permission-assignment.entity';

/**
 * Department Entity
 * Represents departments in the organization
 */
@Entity('departments')
export class Department extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 255,
    comment: 'Name of the department',
  })
  name: string;

  // Relationships
  @OneToMany(() => Team, (team) => team.department)
  teams: Team[];

  @OneToMany(() => UserRoleAssignment, (userRole) => userRole.department)
  userRoles: UserRoleAssignment[];

  @OneToMany(() => RolePermissionAssignment, (rolePermission) => rolePermission.department)
  rolePermissions: RolePermissionAssignment[];
}
