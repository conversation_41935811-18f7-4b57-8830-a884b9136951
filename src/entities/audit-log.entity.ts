import { Entity, Column, <PERSON>To<PERSON><PERSON>, Join<PERSON>olumn, Index } from 'typeorm';
import { BaseEntity } from './base.entity';
import { User } from './user.entity';

export enum AuditAction {
  CREATE = 'create',
  UPDATE = 'update',
  DELETE = 'delete',
  SOFT_DELETE = 'soft_delete',
  RESTORE = 'restore',
  IMPORT = 'import',
  EXPORT = 'export',
  BULK_CREATE = 'bulk_create',
  BULK_UPDATE = 'bulk_update',
  BULK_DELETE = 'bulk_delete',
  LOGIN = 'login',
  LOGOUT = 'logout',
  PASSWORD_CHANGE = 'password_change',
  PERMISSION_CHANGE = 'permission_change',
  ROLE_ASSIGNMENT = 'role_assignment',
  ROLE_REMOVAL = 'role_removal',
}

export enum AuditStatus {
  SUCCESS = 'success',
  FAILED = 'failed',
  PARTIAL = 'partial',
}

/**
 * AuditLog Entity
 * Tracks all data operations and system activities
 */
@Entity('audit_logs')
@Index(['entityType'])
@Index(['entityId'])
@Index(['action'])
@Index(['userId'])
@Index(['status'])
@Index(['createdAt'])
@Index(['ipAddress'])
@Index(['tableName'])
export class AuditLog extends BaseEntity {
  @Column({
    type: 'enum',
    enum: AuditAction,
    comment: 'Type of action performed',
  })
  action: AuditAction;

  @Column({
    type: 'varchar',
    length: 100,
    comment: 'Name of the entity/table affected',
  })
  entityType: string;

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: 'Database table name',
  })
  tableName?: string;

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: 'ID of the specific entity affected',
  })
  entityId?: string;

  @Column({
    type: 'uuid',
    nullable: true,
    comment: 'User who performed the action',
  })
  userId?: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: 'Name of the user who performed the action',
  })
  userName?: string;

  @Column({
    type: 'enum',
    enum: AuditStatus,
    default: AuditStatus.SUCCESS,
    comment: 'Status of the operation',
  })
  status: AuditStatus;

  @Column({
    type: 'varchar',
    length: 45,
    nullable: true,
    comment: 'IP address of the user',
  })
  ipAddress?: string;

  @Column({
    type: 'text',
    nullable: true,
    comment: 'User agent string',
  })
  userAgent?: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: 'API endpoint or URL accessed',
  })
  endpoint?: string;

  @Column({
    type: 'varchar',
    length: 10,
    nullable: true,
    comment: 'HTTP method used',
  })
  httpMethod?: string;

  @Column({
    type: 'json',
    nullable: true,
    comment: 'Data before the change (for updates and deletes)',
  })
  oldValues?: Record<string, any>;

  @Column({
    type: 'json',
    nullable: true,
    comment: 'Data after the change (for creates and updates)',
  })
  newValues?: Record<string, any>;

  @Column({
    type: 'json',
    nullable: true,
    comment: 'Changes made (diff between old and new values)',
  })
  changes?: Record<string, { from: any; to: any }>;

  @Column({
    type: 'text',
    nullable: true,
    comment: 'Description of the action performed',
  })
  description?: string;

  @Column({
    type: 'text',
    nullable: true,
    comment: 'Error message if the operation failed',
  })
  errorMessage?: string;

  @Column({
    type: 'text',
    nullable: true,
    comment: 'Stack trace if an error occurred',
  })
  stackTrace?: string;

  @Column({
    type: 'int',
    nullable: true,
    comment: 'Duration of the operation in milliseconds',
  })
  duration?: number;

  @Column({
    type: 'json',
    nullable: true,
    comment: 'Additional metadata about the operation',
  })
  metadata?: Record<string, any>;

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: 'Session ID',
  })
  sessionId?: string;

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: 'Request ID for tracing',
  })
  requestId?: string;

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
    comment: 'Module or service that performed the action',
  })
  module?: string;

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: 'Method or function that performed the action',
  })
  method?: string;

  @Column({
    type: 'int',
    nullable: true,
    comment: 'Number of records affected (for bulk operations)',
  })
  recordsAffected?: number;

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
    comment: 'Batch ID for bulk operations',
  })
  batchId?: string;

  @Column({
    type: 'json',
    nullable: true,
    comment: 'Query parameters used',
  })
  queryParams?: Record<string, any>;

  @Column({
    type: 'json',
    nullable: true,
    comment: 'Request body data',
  })
  requestBody?: Record<string, any>;

  @Column({
    type: 'int',
    nullable: true,
    comment: 'HTTP response status code',
  })
  responseStatus?: number;

  @Column({
    type: 'boolean',
    default: false,
    comment: 'Whether this action requires approval',
  })
  requiresApproval: boolean;

  @Column({
    type: 'boolean',
    default: false,
    comment: 'Whether this action has been approved',
  })
  isApproved: boolean;

  @Column({
    type: 'uuid',
    nullable: true,
    comment: 'User who approved the action',
  })
  approvedBy?: string;

  @Column({
    type: 'timestamp',
    nullable: true,
    comment: 'When the action was approved',
  })
  approvedAt?: Date;

  @Column({
    type: 'text',
    nullable: true,
    comment: 'Approval notes',
  })
  approvalNotes?: string;

  @Column({
    type: 'varchar',
    length: 50,
    default: 'low',
    comment: 'Risk level of the operation (low, medium, high, critical)',
  })
  riskLevel: string;

  @Column({
    type: 'boolean',
    default: false,
    comment: 'Whether this is a sensitive operation',
  })
  isSensitive: boolean;

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: 'Business context or reason for the action',
  })
  businessContext?: string;

  // Relationships
  @ManyToOne(() => User, (user) => user.id)
  @JoinColumn({ name: 'userId' })
  user?: User;

  @ManyToOne(() => User, (user) => user.id)
  @JoinColumn({ name: 'approvedBy' })
  approver?: User;

  // Helper methods
  get isSuccessful(): boolean {
    return this.status === AuditStatus.SUCCESS;
  }

  get isFailed(): boolean {
    return this.status === AuditStatus.FAILED;
  }

  get isPartial(): boolean {
    return this.status === AuditStatus.PARTIAL;
  }

  get hasChanges(): boolean {
    return this.changes && Object.keys(this.changes).length > 0;
  }

  get changeCount(): number {
    return this.changes ? Object.keys(this.changes).length : 0;
  }
}
