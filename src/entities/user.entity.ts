import { Entity, Column, OneToMany } from 'typeorm';
import { BaseEntity } from './base.entity';
import { UserRoleAssignment } from './user-role-assignment.entity';

/**
 * User Entity
 * Represents users in the system
 */
@Entity('users')
export class User extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 255,
    unique: true,
    comment: 'Unique username for the user',
  })
  username: string;

  @Column({
    type: 'varchar',
    length: 255,
    comment: 'Full name of the user',
  })
  fullName: string;

  @Column({
    type: 'varchar',
    length: 255,
    unique: true,
    comment: 'Email address of the user',
  })
  email: string;

  @Column({
    type: 'varchar',
    length: 20,
    nullable: true,
    comment: 'Phone number of the user',
  })
  phone?: string;

  @Column({
    type: 'varchar',
    length: 255,
    comment: 'Encrypted password',
  })
  password: string;

  @Column({
    type: 'text',
    nullable: true,
    comment: 'Address of the user',
  })
  address?: string;

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: 'City of the user',
  })
  city?: string;

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: 'State of the user',
  })
  state?: string;

  @Column({
    type: 'varchar',
    length: 20,
    nullable: true,
    comment: 'ZIP code of the user',
  })
  zip?: string;

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: 'Country of the user',
  })
  country?: string;

  @Column({
    type: 'varchar',
    length: 50,
    unique: true,
    nullable: true,
    comment: 'Affiliate code for tracking employee performance',
  })
  affiliateCode?: string;

  @Column({
    type: 'uuid',
    nullable: true,
    comment: 'ID of the user who referred this user (affiliate referrer)',
  })
  referredBy?: string;

  @Column({
    type: 'timestamp',
    nullable: true,
    comment: 'Date when the affiliate code was assigned',
  })
  affiliateCodeAssignedAt?: Date;

  @Column({
    type: 'boolean',
    default: true,
    comment: 'Whether the affiliate code is active',
  })
  affiliateCodeActive: boolean;

  // Relationships
  @OneToMany(() => UserRoleAssignment, (userRole) => userRole.user)
  userRoles: UserRoleAssignment[];
}
