import { <PERSON><PERSON><PERSON>, <PERSON>umn, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'typeorm';
import { BaseEntity } from './base.entity';
import { PermissionGroup } from './permission-group.entity';
import { Permission } from './permission.entity';

/**
 * PermissionGroupDetail Entity
 * Junction table between PermissionGroup and Permission
 */
@Entity('permission_group_details')
export class PermissionGroupDetail extends BaseEntity {
  @Column({
    type: 'uuid',
    comment: 'Permission Group ID',
  })
  permissionGroupId: string;

  @Column({
    type: 'uuid',
    comment: 'Permission ID',
  })
  permissionId: string;

  // Relationships
  @ManyToOne(() => PermissionGroup, (permissionGroup) => permissionGroup.permissionDetails)
  @JoinColumn({ name: 'permissionGroupId' })
  permissionGroup: PermissionGroup;

  @ManyToOne(() => Permission, (permission) => permission.permissionGroups)
  @JoinColumn({ name: 'permissionId' })
  permission: Permission;
}
